using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Media;
using MaterialDesignThemes.Wpf;
using PersonalLifeManager.Models;
using PersonalLifeManager.Services;
using System.Windows.Media.Animation;

namespace PersonalLifeManager.Views.Pages
{
    public partial class HabitsPage : Page
    {
        private readonly DatabaseService _databaseService;
        private List<Habit> _allHabits;
        private Habit _currentHabit;
        private DateTime _currentMonth = DateTime.Today;

        public HabitsPage()
        {
            InitializeComponent();
            _databaseService = new DatabaseService();
        }

        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            LoadHabits();
            UpdateCalendarTitle();
            GenerateCalendar();
            
            // Set up calendar navigation buttons
            PreviousMonthButton.Click += PreviousMonthButton_Click;
            NextMonthButton.Click += NextMonthButton_Click;
        }

        private void LoadHabits()
        {
            // Get all habits from database
            _allHabits = _databaseService.GetAllHabits();
            
            // Display habits
            DisplayHabits();
        }

        private void DisplayHabits()
        {
            // Clear current habits
            HabitsPanel.Children.Clear();
            
            if (_allHabits.Any())
            {
                NoHabitsText.Visibility = Visibility.Collapsed;
                
                // Sort habits by name
                var sortedHabits = _allHabits.OrderBy(h => h.Name).ToList();
                
                // Add each habit to the panel
                foreach (var habit in sortedHabits)
                {
                    var habitCard = CreateHabitCard(habit);
                    HabitsPanel.Children.Add(habitCard);
                }
            }
            else
            {
                NoHabitsText.Visibility = Visibility.Visible;
            }
        }

        private void TrackHabit(Habit habit, bool isTracked)
        {
            try
            {
                // Update habit tracking in database
                _databaseService.TrackHabit(habit.Id, DateTime.Today, isTracked);
                
                // Refresh habits to update UI
                LoadHabits();
                
                // Refresh calendar to show updated tracking
                GenerateCalendar();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تتبع العادة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void AddHabitButton_Click(object sender, RoutedEventArgs e)
        {
            // Reset dialog fields
            DialogTitle.Text = "إضافة عادة جديدة";
            HabitNameBox.Text = string.Empty;
            HabitDescriptionBox.Text = string.Empty;
            
            // Set current habit to null (new habit)
            _currentHabit = null;
            
            // Show dialog
            HabitDialogHost.IsOpen = true;
        }
        
        private void EditHabit(Habit habit)
        {
            // Set dialog fields
            DialogTitle.Text = "تعديل العادة";
            HabitNameBox.Text = habit.Name;
            HabitDescriptionBox.Text = habit.Description ?? string.Empty;
            
            // Set current habit
            _currentHabit = habit;
            
            // Show dialog
            HabitDialogHost.IsOpen = true;
        }
        
        private void DeleteHabit(Habit habit)
        {
            // Set current habit
            _currentHabit = habit;
            
            // Show delete confirmation dialog
            DeleteDialogHost.IsOpen = true;
        }
        
        private void SaveHabitButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(HabitNameBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العادة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                if (_currentHabit == null) // New habit
                {
                    // Create new habit
                    var newHabit = new Habit
                    {
                        Name = HabitNameBox.Text.Trim(),
                        Description = string.IsNullOrWhiteSpace(HabitDescriptionBox.Text) ? null : HabitDescriptionBox.Text.Trim(),
                        CreatedAt = DateTime.Now
                    };
                    
                    // Save to database
                    _databaseService.AddHabit(newHabit);
                }
                else // Update existing habit
                {
                    // Update habit properties
                    _currentHabit.Name = HabitNameBox.Text.Trim();
                    _currentHabit.Description = string.IsNullOrWhiteSpace(HabitDescriptionBox.Text) ? null : HabitDescriptionBox.Text.Trim();
                    
                    // Save to database
                    _databaseService.UpdateHabit(_currentHabit);
                }
                
                // Close dialog
                HabitDialogHost.IsOpen = false;
                
                // Refresh habit list
                LoadHabits();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ العادة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void ConfirmDeleteButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Delete habit from database
                _databaseService.DeleteHabit(_currentHabit.Id);
                
                // Close dialog
                DeleteDialogHost.IsOpen = false;
                
                // Refresh habit list
                LoadHabits();
                
                // Refresh calendar
                GenerateCalendar();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف العادة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private UIElement CreateHabitCard(Habit habit)
        {
            // Create card container
            var card = new Card
            {
                Margin = new Thickness(0, 0, 0, 10),
                UniformCornerRadius = 8
            };
            
            // Create main grid
            var grid = new Grid { Margin = new Thickness(15) };
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            
            // Habit info panel
            var infoPanel = new StackPanel { Margin = new Thickness(0, 0, 10, 0) };
            Grid.SetColumn(infoPanel, 0);
            
            // Habit name
            var nameText = new TextBlock
            {
                Text = habit.Name,
                FontSize = 16,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 5)
            };
            infoPanel.Children.Add(nameText);
            
            // Habit description (if any)
            if (!string.IsNullOrWhiteSpace(habit.Description))
            {
                var descriptionText = new TextBlock
                {
                    Text = habit.Description,
                    FontSize = 12,
                    TextWrapping = TextWrapping.Wrap,
                    Opacity = 0.7,
                    Margin = new Thickness(0, 0, 0, 5)
                };
                infoPanel.Children.Add(descriptionText);
            }
            
            // Streak info
            var streakPanel = new StackPanel { Orientation = Orientation.Horizontal };
            
            var streakIcon = new PackIcon
            {
                Kind = PackIconKind.Fire,
                Width = 16,
                Height = 16,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F39319")),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 5, 0)
            };
            streakPanel.Children.Add(streakIcon);
            
            var streakText = new TextBlock
            {
                Text = $"التتابع الحالي: {habit.CurrentStreak} يوم",
                FontSize = 12,
                VerticalAlignment = VerticalAlignment.Center
            };
            streakPanel.Children.Add(streakText);
            
            infoPanel.Children.Add(streakPanel);
            grid.Children.Add(infoPanel);
            
            // Action buttons panel
            var actionsPanel = new StackPanel { Orientation = Orientation.Horizontal };
            Grid.SetColumn(actionsPanel, 1);
            
            // Track button
            var trackButton = new ToggleButton
            {
                IsChecked = habit.IsTrackedToday,
                ToolTip = habit.IsTrackedToday ? "إلغاء تتبع اليوم" : "تتبع اليوم",
                Style = Application.Current.Resources["MaterialDesignActionToggleButton"] as Style,
                Width = 40,
                Height = 40,
                Margin = new Thickness(0, 0, 5, 0)
            };
            
            var trackCheckedIcon = new PackIcon
            {
                Kind = PackIconKind.Check,
                Width = 20,
                Height = 20
            };
            trackButton.Content = trackCheckedIcon;
            
            var trackUncheckedIcon = new PackIcon
            {
                Kind = PackIconKind.Plus,
                Width = 20,
                Height = 20
            };
            trackButton.Tag = trackUncheckedIcon;
            
            trackButton.Checked += (s, e) => TrackHabit(habit, true);
            trackButton.Unchecked += (s, e) => TrackHabit(habit, false);
            actionsPanel.Children.Add(trackButton);
            
            // Edit button
            var editButton = new Button
            {
                Style = Application.Current.Resources["MaterialDesignFlatButton"] as Style,
                ToolTip = "تعديل",
                Width = 40,
                Height = 40,
                Padding = new Thickness(0),
                Margin = new Thickness(0, 0, 5, 0)
            };
            var editIcon = new PackIcon
            {
                Kind = PackIconKind.Pencil,
                Width = 20,
                Height = 20
            };
            editButton.Content = editIcon;
            editButton.Click += (s, e) => EditHabit(habit);
            actionsPanel.Children.Add(editButton);
            
            // Delete button
            var deleteButton = new Button
            {
                Style = Application.Current.Resources["MaterialDesignFlatButton"] as Style,
                ToolTip = "حذف",
                Width = 40,
                Height = 40,
                Padding = new Thickness(0)
            };
            var deleteIcon = new PackIcon
            {
                Kind = PackIconKind.Delete,
                Width = 20,
                Height = 20
            };
            deleteButton.Content = deleteIcon;
            deleteButton.Click += (s, e) => DeleteHabit(habit);
            actionsPanel.Children.Add(deleteButton);
            
            grid.Children.Add(actionsPanel);
            card.Content = grid;
            
            return card;
        }
        
        private void UpdateCalendarTitle()
        {
            // Update month/year title
            MonthYearText.Text = _currentMonth.ToString("MMMM yyyy");
        }
        
        private void PreviousMonthButton_Click(object sender, RoutedEventArgs e)
        {
            // Go to previous month
            _currentMonth = _currentMonth.AddMonths(-1);
            UpdateCalendarTitle();
            GenerateCalendar();
        }
        
        private void NextMonthButton_Click(object sender, RoutedEventArgs e)
        {
            // Go to next month
            _currentMonth = _currentMonth.AddMonths(1);
            UpdateCalendarTitle();
            GenerateCalendar();
        }
        
        private void GenerateCalendar()
        {
            // Clear current calendar
            CalendarGrid.Children.Clear();
            CalendarGrid.RowDefinitions.Clear();
            CalendarGrid.ColumnDefinitions.Clear();
            
            // Setup grid columns (days of week)
            for (int i = 0; i < 7; i++)
            {
                CalendarGrid.ColumnDefinitions.Add(new ColumnDefinition());
            }
            
            // Add day headers
            string[] dayNames = { "الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت" };
            for (int i = 0; i < 7; i++)
            {
                var dayHeader = new TextBlock
                {
                    Text = dayNames[i],
                    FontWeight = FontWeights.SemiBold,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 5, 0, 10)
                };
                Grid.SetRow(dayHeader, 0);
                Grid.SetColumn(dayHeader, i);
                CalendarGrid.Children.Add(dayHeader);
            }
            
            // Add row for day headers
            CalendarGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            
            // Get first day of month and number of days
            var firstDayOfMonth = new DateTime(_currentMonth.Year, _currentMonth.Month, 1);
            int daysInMonth = DateTime.DaysInMonth(_currentMonth.Year, _currentMonth.Month);
            
            // Calculate rows needed (including header row)
            int firstDayOfWeek = (int)firstDayOfMonth.DayOfWeek;
            int totalDays = firstDayOfWeek + daysInMonth;
            int rowsNeeded = (int)Math.Ceiling(totalDays / 7.0);
            
            // Add rows for days
            for (int i = 0; i < rowsNeeded; i++)
            {
                CalendarGrid.RowDefinitions.Add(new RowDefinition());
            }
            
            // Add day cells
            int currentDay = 1;
            for (int row = 1; row <= rowsNeeded; row++)
            {
                for (int col = 0; col < 7; col++)
                {
                    // Skip cells before first day of month
                    if (row == 1 && col < firstDayOfWeek)
                    {
                        continue;
                    }
                    
                    // Stop after last day of month
                    if (currentDay > daysInMonth)
                    {
                        break;
                    }
                    
                    // Create day cell
                    var dayCell = CreateDayCell(currentDay);
                    Grid.SetRow(dayCell, row);
                    Grid.SetColumn(dayCell, col);
                    CalendarGrid.Children.Add(dayCell);
                    
                    currentDay++;
                }
            }
        }
        
        private UIElement CreateDayCell(int day)
        {
            // Create date for this cell
            var cellDate = new DateTime(_currentMonth.Year, _currentMonth.Month, day);
            bool isToday = cellDate.Date == DateTime.Today;
            
            // Create border container
            var border = new Border
            {
                BorderThickness = new Thickness(1),
                BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3D3D3D")),
                Background = isToday ? new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3D3D3D")) : null,
                Margin = new Thickness(2),
                MinHeight = 60
            };
            
            // Create stack panel for content
            var panel = new StackPanel { Margin = new Thickness(5) };
            
            // Day number
            var dayText = new TextBlock
            {
                Text = day.ToString(),
                FontWeight = isToday ? FontWeights.Bold : FontWeights.Normal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 5)
            };
            panel.Children.Add(dayText);
            
            // Add habit indicators
            if (_allHabits != null && _allHabits.Any())
            {
                foreach (var habit in _allHabits)
                {
                    if (habit.IsTrackedOnDate(cellDate))
                    {
                        var habitIndicator = new Border
                        {
                            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F39319")),
                            CornerRadius = new CornerRadius(2),
                            Height = 4,
                            Margin = new Thickness(2, 2, 2, 0)
                        };
                        panel.Children.Add(habitIndicator);
                    }
                }
            }
            
            border.Child = panel;
            return border;
        }