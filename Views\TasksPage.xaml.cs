using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;
using PersonalLifeManager.Models;
using PersonalLifeManager.Services;

namespace PersonalLifeManager.Views
{
    public partial class TasksPage : Page
    {
        private readonly DatabaseService _databaseService;
        public ObservableCollection<TaskItem> Tasks { get; set; }
        private TaskItem _currentEditingTask;

        public TasksPage()
        {
            InitializeComponent();
            _databaseService = new DatabaseService();
            Tasks = new ObservableCollection<TaskItem>();
            DataContext = this;
            LoadTasks();
        }

        private void LoadTasks()
        {
            Tasks.Clear();
            var tasks = _databaseService.GetAllTasks();
            
            // Add tasks with staggered animation
            for (int i = 0; i < tasks.Count; i++)
            {
                var task = tasks[i];
                Tasks.Add(task);
                
                // Apply animation to task items in the ListView
                if (TasksListView.ItemContainerGenerator.ContainerFromItem(task) is FrameworkElement container)
                {
                    // Set initial state for animation
                    container.Opacity = 0;
                    container.RenderTransform = new System.Windows.Media.TranslateTransform(50, 0);
                    
                    // Create animations
                    var fadeAnimation = new System.Windows.Media.Animation.DoubleAnimation
                    {
                        From = 0,
                        To = 1,
                        Duration = TimeSpan.FromMilliseconds(300),
                        BeginTime = TimeSpan.FromMilliseconds(i * 100) // Stagger the animations
                    };
                    
                    var moveAnimation = new System.Windows.Media.Animation.DoubleAnimation
                    {
                        From = 50,
                        To = 0,
                        Duration = TimeSpan.FromMilliseconds(300),
                        BeginTime = TimeSpan.FromMilliseconds(i * 100),
                        EasingFunction = new System.Windows.Media.Animation.CubicEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut }
                    };
                    
                    // Start animations
                    container.BeginAnimation(UIElement.OpacityProperty, fadeAnimation);
                    ((System.Windows.Media.TranslateTransform)container.RenderTransform).BeginAnimation(System.Windows.Media.TranslateTransform.XProperty, moveAnimation);
                }
            }
        }

        private void AddTaskButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(TaskTitleInput.Text))
            {
                ShowSnackbar("يرجى إدخال عنوان للمهمة");
                return;
            }

            if (TaskDueDatePicker.SelectedDate == null)
            {
                ShowSnackbar("يرجى تحديد تاريخ استحقاق للمهمة");
                return;
            }

            var selectedPriorityItem = TaskPriorityComboBox.SelectedItem as ComboBoxItem;
            if (selectedPriorityItem == null)
            {
                ShowSnackbar("يرجى تحديد أولوية للمهمة");
                return;
            }

            var priorityString = selectedPriorityItem.Tag.ToString();
            TaskPriority priority = Enum.Parse<TaskPriority>(priorityString);

            var newTask = new TaskItem
            {
                Title = TaskTitleInput.Text,
                Description = TaskDescriptionInput.Text,
                DueDate = TaskDueDatePicker.SelectedDate.Value,
                Priority = priority,
                IsCompleted = false,
                CreatedAt = DateTime.Now
            };

            if (_currentEditingTask != null)
            {
                // Update existing task
                newTask.Id = _currentEditingTask.Id;
                _databaseService.UpdateTask(newTask);
                ShowSnackbar("تم تحديث المهمة بنجاح");
                _currentEditingTask = null;
                AddTaskButton.Content = "إضافة المهمة";
            }
            else
            {
                // Add new task
                var taskId = _databaseService.AddTask(newTask);
                newTask.Id = taskId;
                ShowSnackbar("تمت إضافة المهمة بنجاح");
            }

            // Clear inputs
            TaskTitleInput.Text = string.Empty;
            TaskDescriptionInput.Text = string.Empty;
            TaskDueDatePicker.SelectedDate = null;
            TaskPriorityComboBox.SelectedIndex = 1; // Medium priority

            // Refresh tasks list
            LoadTasks();
        }

        private void EditTaskButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var task = button.DataContext as TaskItem;

            // Populate form with task data
            TaskTitleInput.Text = task.Title;
            TaskDescriptionInput.Text = task.Description;
            TaskDueDatePicker.SelectedDate = task.DueDate;

            // Select the appropriate priority
            switch (task.Priority)
            {
                case TaskPriority.Low:
                    TaskPriorityComboBox.SelectedIndex = 0;
                    break;
                case TaskPriority.Medium:
                    TaskPriorityComboBox.SelectedIndex = 1;
                    break;
                case TaskPriority.High:
                    TaskPriorityComboBox.SelectedIndex = 2;
                    break;
            }

            // Set current editing task
            _currentEditingTask = task;
            AddTaskButton.Content = "تحديث المهمة";
        }

        private void DeleteTaskButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var task = button.DataContext as TaskItem;

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المهمة '{task.Title}'؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                _databaseService.DeleteTask(task.Id);
                ShowSnackbar("تم حذف المهمة بنجاح");
                LoadTasks();
            }
        }

        private void TaskCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            var checkbox = sender as CheckBox;
            var task = checkbox.DataContext as TaskItem;
            task.IsCompleted = true;
            _databaseService.UpdateTask(task);
            LoadTasks();
        }

        private void TaskCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            var checkbox = sender as CheckBox;
            var task = checkbox.DataContext as TaskItem;
            task.IsCompleted = false;
            _databaseService.UpdateTask(task);
            LoadTasks();
        }

        private void ShowSnackbar(string message)
        {
            var snackbar = new Snackbar();
            snackbar.MessageQueue.Enqueue(message, null, null, null, false, true, TimeSpan.FromSeconds(3));
        }
    }
}