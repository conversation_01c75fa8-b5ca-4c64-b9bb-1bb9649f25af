<svg xmlns="http://www.w3.org/2000/svg" width="1000" height="1000" viewBox="0 0 1000 1000">
  <defs>
    <pattern id="smallGrid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#F0F0F0" stroke-width="0.5" opacity="0.2"/>
    </pattern>
    <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
      <rect width="100" height="100" fill="url(#smallGrid)"/>
      <path d="M 100 0 L 0 0 0 100" fill="none" stroke="#F0F0F0" stroke-width="1" opacity="0.3"/>
    </pattern>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#262626" />
      <stop offset="100%" stop-color="#333333" />
    </linearGradient>
  </defs>
  
  <!-- Background with gradient -->
  <rect width="100%" height="100%" fill="url(#bgGradient)" />
  
  <!-- Grid overlay -->
  <rect width="100%" height="100%" fill="url(#grid)" />
  
  <!-- Decorative elements -->
  <circle cx="100" cy="100" r="50" fill="#F39319" opacity="0.1" />
  <circle cx="900" cy="900" r="70" fill="#F39319" opacity="0.1" />
  <circle cx="900" cy="100" r="30" fill="#F39319" opacity="0.1" />
  <circle cx="100" cy="900" r="40" fill="#F39319" opacity="0.1" />
  
  <!-- Abstract lines -->
  <path d="M0,200 Q500,0 1000,200" stroke="#F39319" stroke-width="2" fill="none" opacity="0.1" />
  <path d="M0,800 Q500,1000 1000,800" stroke="#F39319" stroke-width="2" fill="none" opacity="0.1" />
</svg>