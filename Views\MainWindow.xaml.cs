using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using PersonalLifeManager.Services;
using PersonalLifeManager.Views.Pages;

namespace PersonalLifeManager.Views
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            
            // Navigate to default page (Tasks)
            NavigateToPage("TasksPage");
        }

        private void NavigationButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button)
            {
                // Highlight the selected button
                HighlightSelectedButton(button);
                
                // Navigate to the selected page
                string pageName = button.Tag.ToString();
                NavigateToPage(pageName);
            }
        }
        
        private void HighlightSelectedButton(Button selectedButton)
        {
            // Reset all navigation buttons to default style
            foreach (var child in ((StackPanel)selectedButton.Parent).Children)
            {
                if (child is Button navButton)
                {
                    // Apply subtle animation to deselected buttons
                    DoubleAnimation opacityAnimation = new DoubleAnimation
                    {
                        To = 0.7,
                        Duration = TimeSpan.FromMilliseconds(200)
                    };
                    navButton.BeginAnimation(OpacityProperty, opacityAnimation);
                }
            }
            
            // Highlight the selected button with animation
            DoubleAnimation selectedOpacityAnimation = new DoubleAnimation
            {
                To = 1.0,
                Duration = TimeSpan.FromMilliseconds(200)
            };
            selectedButton.BeginAnimation(OpacityProperty, selectedOpacityAnimation);
            
            // Add a subtle bounce effect to the selected button
            ScaleTransform scaleTransform = new ScaleTransform(1, 1);
            selectedButton.RenderTransform = scaleTransform;
            
            DoubleAnimation scaleXAnimation = new DoubleAnimation
            {
                From = 1.0,
                To = 1.05,
                Duration = TimeSpan.FromMilliseconds(100),
                AutoReverse = true
            };
            
            DoubleAnimation scaleYAnimation = new DoubleAnimation
            {
                From = 1.0,
                To = 1.05,
                Duration = TimeSpan.FromMilliseconds(100),
                AutoReverse = true
            };
            
            scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleXAnimation);
            scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleYAnimation);
        }

        public void NavigateToPage(string pageName)
        {
            Page page = null;
            
            // Update page title
            switch (pageName)
            {
                case "TasksPage":
                    page = new TasksPage();
                    PageTitle.Text = "المهام والمواعيد";
                    break;
                case "CoursesPage":
                    page = new CoursesPage();
                    PageTitle.Text = "الكورسات";
                    break;
                case "HabitsPage":
                    page = new HabitsPage();
                    PageTitle.Text = "العادات";
                    break;
                case "CalendarPage":
                    page = new CalendarPage();
                    PageTitle.Text = "التقويم";
                    break;
            }
            
            if (page != null)
            {
                // Create page transition animation
                AnimatePageTransition(page);
            }
        }
        
        private void AnimatePageTransition(Page newPage)
        {
            // Create slide and fade animations for smooth page transitions
            DoubleAnimation fadeOutAnimation = new DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(150)
            };
            
            fadeOutAnimation.Completed += (s, e) =>
            {
                // Navigate to the new page after fade out
                ContentFrame.Navigate(newPage);
                
                // Create fade in animation
                DoubleAnimation fadeInAnimation = new DoubleAnimation
                {
                    From = 0,
                    To = 1,
                    Duration = TimeSpan.FromMilliseconds(250)
                };
                
                // Create slide in animation for the page title
                ThicknessAnimation slideAnimation = new ThicknessAnimation
                {
                    From = new Thickness(30, -10, 30, 10),
                    To = new Thickness(30, 20, 30, 10),
                    Duration = TimeSpan.FromMilliseconds(300),
                    EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
                };
                
                // Apply animations
                ContentFrame.BeginAnimation(OpacityProperty, fadeInAnimation);
                PageTitle.BeginAnimation(MarginProperty, slideAnimation);
                PageTitle.BeginAnimation(OpacityProperty, fadeInAnimation);
            };
            
            // Start the fade out animation if there's content
            if (ContentFrame.Content != null)
            {
                ContentFrame.BeginAnimation(OpacityProperty, fadeOutAnimation);
            }
            else
            {
                // If there's no content yet, just navigate directly
                ContentFrame.Navigate(newPage);
            }
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            // Create fade out animation for main window
            DoubleAnimation fadeOutAnimation = new DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(300)
            };
            
            fadeOutAnimation.Completed += (s, args) =>
            {
                // Show login window and close this window after animation completes
                var loginWindow = new LoginWindow();
                loginWindow.Show();
                this.Close();
            };
            
            // Start the fade out animation
            this.BeginAnimation(OpacityProperty, fadeOutAnimation);
        }


    }
}