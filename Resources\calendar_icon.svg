<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
  <defs>
    <linearGradient id="calendarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#F39319" />
      <stop offset="100%" stop-color="#F5A742" />
    </linearGradient>
  </defs>
  
  <!-- Calendar base -->
  <rect x="3" y="4" width="18" height="16" rx="2" fill="#F0F0F0" stroke="#262626" stroke-width="1.5" />
  
  <!-- Calendar top bar -->
  <rect x="3" y="4" width="18" height="4" rx="2" fill="url(#calendarGradient)" stroke="#262626" stroke-width="1.5" />
  
  <!-- Calendar hangers -->
  <rect x="7" y="2" width="2" height="4" rx="1" fill="#262626" />
  <rect x="15" y="2" width="2" height="4" rx="1" fill="#262626" />
  
  <!-- Calendar grid lines -->
  <line x1="7" y1="12" x2="17" y2="12" stroke="#262626" stroke-width="1" />
  <line x1="7" y1="16" x2="17" y2="16" stroke="#262626" stroke-width="1" />
  <line x1="9" y1="8" x2="9" y2="20" stroke="#262626" stroke-width="1" />
  <line x1="12" y1="8" x2="12" y2="20" stroke="#262626" stroke-width="1" />
  <line x1="15" y1="8" x2="15" y2="20" stroke="#262626" stroke-width="1" />
  
  <!-- Current day highlight -->
  <rect x="12.5" y="12.5" width="2" height="3" rx="0.5" fill="url(#calendarGradient)" stroke="#262626" stroke-width="0.5" />
  
  <!-- Event indicators -->
  <circle cx="10" cy="10" r="1" fill="#262626" />
  <circle cx="14" cy="10" r="1" fill="#262626" />
  <circle cx="10" cy="14" r="1" fill="#262626" />
  <circle cx="10" cy="18" r="1" fill="#262626" />
</svg>