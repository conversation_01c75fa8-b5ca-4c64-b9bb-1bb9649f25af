using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace PersonalLifeManager
{
    public class BrushLightenConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is SolidColorBrush brush)
            {
                double factor = 0.15; // Default lighten factor
                if (parameter != null && double.TryParse(parameter.ToString(), out double paramFactor))
                {
                    factor = paramFactor;
                }

                Color color = brush.Color;
                
                // Lighten the color
                byte r = (byte)Math.Min(255, color.R + (255 - color.R) * factor);
                byte g = (byte)Math.Min(255, color.G + (255 - color.G) * factor);
                byte b = (byte)Math.Min(255, color.B + (255 - color.B) * factor);
                
                return new SolidColorBrush(Color.FromArgb(color.A, r, g, b));
            }

            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}