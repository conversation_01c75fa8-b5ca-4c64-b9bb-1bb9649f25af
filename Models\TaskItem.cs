using System;

namespace PersonalLifeManager.Models
{
    public enum TaskPriority
    {
        Low = 0,
        Medium = 1,
        High = 2
    }

    public class TaskItem
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string? Description { get; set; }
        public DateTime? DueDate { get; set; }
        public TaskPriority Priority { get; set; } = TaskPriority.Medium;
        public bool IsCompleted { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Helper properties for UI
        public string PriorityText => Priority switch
        {
            TaskPriority.Low => "منخفضة",
            TaskPriority.Medium => "متوسطة",
            TaskPriority.High => "عالية",
            _ => "متوسطة"
        };
        
        public string DueDateText => DueDate?.ToString("yyyy-MM-dd") ?? "لا يوجد تاريخ استحقاق";
        public string StatusText => IsCompleted ? "مكتملة" : "قيد التنفيذ";
    }
}