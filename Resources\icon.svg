<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#262626" />
      <stop offset="100%" stop-color="#404040" />
    </linearGradient>
  </defs>
  <circle cx="32" cy="32" r="30" fill="url(#gradient)" />
  <circle cx="32" cy="32" r="26" fill="#F0F0F0" />
  <g transform="translate(16, 16)">
    <!-- Calendar Icon -->
    <rect x="0" y="0" width="16" height="16" rx="2" fill="#F39319" />
    <rect x="2" y="4" width="12" height="10" rx="1" fill="#F0F0F0" />
    <line x1="4" y1="8" x2="12" y2="8" stroke="#262626" stroke-width="1" />
    <line x1="4" y1="10" x2="12" y2="10" stroke="#262626" stroke-width="1" />
    <line x1="4" y1="12" x2="8" y2="12" stroke="#262626" stroke-width="1" />
    <!-- Calendar Top Pins -->
    <rect x="4" y="0" width="2" height="2" rx="0.5" fill="#262626" />
    <rect x="10" y="0" width="2" height="2" rx="0.5" fill="#262626" />
  </g>
  <!-- Clock Hands -->
  <line x1="32" y1="32" x2="32" y2="20" stroke="#F39319" stroke-width="2" stroke-linecap="round" />
  <line x1="32" y1="32" x2="40" y2="36" stroke="#F39319" stroke-width="2" stroke-linecap="round" />
  <!-- Center Dot -->
  <circle cx="32" cy="32" r="2" fill="#262626" />
</svg>