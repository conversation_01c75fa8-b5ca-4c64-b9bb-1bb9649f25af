<Window x:Class="PersonalLifeManager.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:PersonalLifeManager.Views"
        xmlns:System="clr-namespace:System;assembly=mscorlib"
        mc:Ignorable="d"
        Title="Personal Life Manager" 
        Height="700" Width="1100"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource PrimaryBackgroundBrush}">
    
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <!-- Navigation Panel -->
        <Grid Grid.Column="0" Background="#333333">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!-- App Title with Animation -->
            <StackPanel Grid.Row="0" Margin="20,30,20,40">
                <TextBlock Text="Personal" 
                           FontSize="24" FontWeight="Bold" 
                           Foreground="{StaticResource PrimaryTextBrush}">
                    <TextBlock.Triggers>
                        <EventTrigger RoutedEvent="TextBlock.Loaded">
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation 
                                        Storyboard.TargetProperty="Opacity"
                                        From="0" To="1" Duration="0:0:1" />
                                    <ThicknessAnimation 
                                        Storyboard.TargetProperty="Margin"
                                        From="0,-20,0,0" To="0,0,0,0" Duration="0:0:0.7">
                                        <ThicknessAnimation.EasingFunction>
                                            <ElasticEase EasingMode="EaseOut" Oscillations="2" Springiness="3" />
                                        </ThicknessAnimation.EasingFunction>
                                    </ThicknessAnimation>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                    </TextBlock.Triggers>
                </TextBlock>
                <TextBlock Text="Life Manager" 
                           FontSize="24" FontWeight="Bold" 
                           Foreground="{StaticResource AccentBrush}">
                    <TextBlock.Triggers>
                        <EventTrigger RoutedEvent="TextBlock.Loaded">
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation 
                                        Storyboard.TargetProperty="Opacity"
                                        From="0" To="1" Duration="0:0:1" 
                                        BeginTime="0:0:0.3" />
                                    <ThicknessAnimation 
                                        Storyboard.TargetProperty="Margin"
                                        From="0,-20,0,0" To="0,0,0,0" Duration="0:0:0.7"
                                        BeginTime="0:0:0.3">
                                        <ThicknessAnimation.EasingFunction>
                                            <ElasticEase EasingMode="EaseOut" Oscillations="2" Springiness="3" />
                                        </ThicknessAnimation.EasingFunction>
                                    </ThicknessAnimation>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                    </TextBlock.Triggers>
                </TextBlock>
            </StackPanel>

            <!-- Navigation Buttons with Animation -->
            <StackPanel Grid.Row="1" Margin="10,0">
                <Button x:Name="TasksButton" 
                        Content="المهام والمواعيد" 
                        Style="{StaticResource NavigationButton}"
                        Click="NavigationButton_Click" 
                        Tag="TasksPage">
                    <Button.Triggers>
                        <EventTrigger RoutedEvent="Button.Loaded">
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation 
                                        Storyboard.TargetProperty="Opacity"
                                        From="0" To="1" Duration="0:0:0.5" 
                                        BeginTime="0:0:0.2" />
                                    <ThicknessAnimation 
                                        Storyboard.TargetProperty="Margin"
                                        From="-50,0,0,0" To="0,0,0,0" Duration="0:0:0.5" 
                                        BeginTime="0:0:0.2">
                                        <ThicknessAnimation.EasingFunction>
                                            <CubicEase EasingMode="EaseOut" />
                                        </ThicknessAnimation.EasingFunction>
                                    </ThicknessAnimation>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                    </Button.Triggers>
                </Button>
                
                <Button x:Name="CoursesButton" 
                        Content="الكورسات" 
                        Style="{StaticResource NavigationButton}"
                        Click="NavigationButton_Click" 
                        Tag="CoursesPage">
                    <Button.Triggers>
                        <EventTrigger RoutedEvent="Button.Loaded">
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation 
                                        Storyboard.TargetProperty="Opacity"
                                        From="0" To="1" Duration="0:0:0.5" 
                                        BeginTime="0:0:0.4" />
                                    <ThicknessAnimation 
                                        Storyboard.TargetProperty="Margin"
                                        From="-50,0,0,0" To="0,0,0,0" Duration="0:0:0.5" 
                                        BeginTime="0:0:0.4">
                                        <ThicknessAnimation.EasingFunction>
                                            <CubicEase EasingMode="EaseOut" />
                                        </ThicknessAnimation.EasingFunction>
                                    </ThicknessAnimation>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                    </Button.Triggers>
                </Button>
                
                <Button x:Name="HabitsButton" 
                        Content="العادات" 
                        Style="{StaticResource NavigationButton}"
                        Click="NavigationButton_Click" 
                        Tag="HabitsPage">
                    <Button.Triggers>
                        <EventTrigger RoutedEvent="Button.Loaded">
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation 
                                        Storyboard.TargetProperty="Opacity"
                                        From="0" To="1" Duration="0:0:0.5" 
                                        BeginTime="0:0:0.6" />
                                    <ThicknessAnimation 
                                        Storyboard.TargetProperty="Margin"
                                        From="-50,0,0,0" To="0,0,0,0" Duration="0:0:0.5" 
                                        BeginTime="0:0:0.6">
                                        <ThicknessAnimation.EasingFunction>
                                            <CubicEase EasingMode="EaseOut" />
                                        </ThicknessAnimation.EasingFunction>
                                    </ThicknessAnimation>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                    </Button.Triggers>
                </Button>
                
                <Button x:Name="CalendarButton" 
                        Content="التقويم" 
                        Style="{StaticResource NavigationButton}"
                        Click="NavigationButton_Click" 
                        Tag="CalendarPage">
                    <Button.Triggers>
                        <EventTrigger RoutedEvent="Button.Loaded">
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation 
                                        Storyboard.TargetProperty="Opacity"
                                        From="0" To="1" Duration="0:0:0.5" 
                                        BeginTime="0:0:0.8" />
                                    <ThicknessAnimation 
                                        Storyboard.TargetProperty="Margin"
                                        From="-50,0,0,0" To="0,0,0,0" Duration="0:0:0.5" 
                                        BeginTime="0:0:0.8">
                                        <ThicknessAnimation.EasingFunction>
                                            <CubicEase EasingMode="EaseOut" />
                                        </ThicknessAnimation.EasingFunction>
                                    </ThicknessAnimation>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                    </Button.Triggers>
                </Button>
            </StackPanel>

            <!-- Logout Button with Animation -->
            <Button Grid.Row="2" 
                    Content="تسجيل الخروج" 
                    Style="{StaticResource NavigationButton}"
                    Click="LogoutButton_Click"
                    Margin="10,20">
                <Button.Triggers>
                    <EventTrigger RoutedEvent="Button.Loaded">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation 
                                    Storyboard.TargetProperty="Opacity"
                                    From="0" To="1" Duration="0:0:0.5" 
                                    BeginTime="0:0:1" />
                                <ThicknessAnimation 
                                    Storyboard.TargetProperty="Margin"
                                    From="10,-20,10,20" To="10,20,10,20" Duration="0:0:0.5" 
                                    BeginTime="0:0:1">
                                    <ThicknessAnimation.EasingFunction>
                                        <CubicEase EasingMode="EaseOut" />
                                    </ThicknessAnimation.EasingFunction>
                                </ThicknessAnimation>
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                </Button.Triggers>
            </Button>
        </Grid>

        <!-- Content Area -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!-- Page Title with Animation -->
            <TextBlock x:Name="PageTitle" 
                       Grid.Row="0" 
                       Text="المهام والمواعيد" 
                       Style="{StaticResource PageTitle}" 
                       Margin="30,20,30,10">
                <TextBlock.Triggers>
                    <EventTrigger RoutedEvent="TextBlock.Loaded">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation 
                                    Storyboard.TargetProperty="Opacity"
                                    From="0" To="1" Duration="0:0:0.7" 
                                    BeginTime="0:0:0.5" />
                                <ThicknessAnimation 
                                    Storyboard.TargetProperty="Margin"
                                    From="30,-10,30,10" To="30,20,30,10" Duration="0:0:0.7" 
                                    BeginTime="0:0:0.5">
                                    <ThicknessAnimation.EasingFunction>
                                        <CubicEase EasingMode="EaseOut" />
                                    </ThicknessAnimation.EasingFunction>
                                </ThicknessAnimation>
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                </TextBlock.Triggers>
            </TextBlock>

            <!-- Content Frame with Animation -->
            <Frame x:Name="ContentFrame" 
                   Grid.Row="1" 
                   NavigationUIVisibility="Hidden" 
                   Margin="20,0,20,20">
                <Frame.Triggers>
                    <EventTrigger RoutedEvent="Frame.Loaded">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation 
                                    Storyboard.TargetProperty="Opacity"
                                    From="0" To="1" Duration="0:0:0.7" 
                                    BeginTime="0:0:0.7" />
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                </Frame.Triggers>
            </Frame>
        </Grid>
    </Grid>
</Window>