<Page x:Class="PersonalLifeManager.Views.Pages.CalendarPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:PersonalLifeManager.Views.Pages"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="800"
      Loaded="Page_Loaded"
      Title="CalendarPage">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Calendar Controls -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Month and Year Display -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock x:Name="MonthYearText" 
                           FontSize="24" 
                           FontWeight="SemiBold" 
                           Text="يناير 2023"/>
            </StackPanel>

            <!-- Navigation Buttons -->
            <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Style="{StaticResource MaterialDesignFlatButton}" 
                        Click="PreviousMonth_Click" 
                        ToolTip="الشهر السابق">
                    <materialDesign:PackIcon Kind="ChevronLeft" Width="24" Height="24"/>
                </Button>
                <Button Style="{StaticResource MaterialDesignFlatButton}" 
                        Click="CurrentMonth_Click" 
                        ToolTip="الشهر الحالي">
                    <materialDesign:PackIcon Kind="CalendarToday" Width="24" Height="24"/>
                </Button>
                <Button Style="{StaticResource MaterialDesignFlatButton}" 
                        Click="NextMonth_Click" 
                        ToolTip="الشهر التالي">
                    <materialDesign:PackIcon Kind="ChevronRight" Width="24" Height="24"/>
                </Button>
            </StackPanel>
        </Grid>

        <!-- Calendar Grid -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Days of Week Header -->
            <Grid Grid.Row="0" Margin="0,0,0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="الأحد" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                <TextBlock Grid.Column="1" Text="الإثنين" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                <TextBlock Grid.Column="2" Text="الثلاثاء" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                <TextBlock Grid.Column="3" Text="الأربعاء" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                <TextBlock Grid.Column="4" Text="الخميس" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                <TextBlock Grid.Column="5" Text="الجمعة" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                <TextBlock Grid.Column="6" Text="السبت" HorizontalAlignment="Center" FontWeight="SemiBold"/>
            </Grid>

            <!-- Calendar Days Grid -->
            <Grid x:Name="CalendarGrid" Grid.Row="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Calendar days will be added here programmatically -->
            </Grid>
        </Grid>

        <!-- Day Details Dialog -->
        <materialDesign:DialogHost x:Name="DayDetailsDialogHost" Grid.RowSpan="2">
            <materialDesign:DialogHost.DialogContent>
                <Grid Width="400" Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Dialog Header -->
                    <TextBlock x:Name="SelectedDateText" 
                               Grid.Row="0" 
                               Text="1 يناير 2023" 
                               FontSize="18" 
                               FontWeight="SemiBold" 
                               Margin="0,0,0,15"/>

                    <!-- Day Items List -->
                    <ScrollViewer Grid.Row="1" MaxHeight="300" VerticalScrollBarVisibility="Auto">
                        <StackPanel x:Name="DayItemsPanel">
                            <!-- Items will be added here programmatically -->
                            <TextBlock x:Name="NoDayItemsText" 
                                       Text="لا توجد مهام أو مواعيد في هذا اليوم" 
                                       Opacity="0.7" 
                                       HorizontalAlignment="Center" 
                                       Margin="0,20,0,0"/>
                        </StackPanel>
                    </ScrollViewer>

                    <!-- Dialog Actions -->
                    <Button Grid.Row="2" 
                            Style="{StaticResource MaterialDesignFlatButton}" 
                            Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}" 
                            Content="إغلاق" 
                            Margin="0,15,0,0" 
                            HorizontalAlignment="Right"/>
                </Grid>
            </materialDesign:DialogHost.DialogContent>
        </materialDesign:DialogHost>
    </Grid>
</Page>