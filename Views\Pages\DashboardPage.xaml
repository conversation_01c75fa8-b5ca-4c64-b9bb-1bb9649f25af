<Page x:Class="PersonalLifeManager.Views.Pages.DashboardPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:PersonalLifeManager.Views.Pages"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="800"
      Title="DashboardPage"
      Loaded="Page_Loaded">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Welcome Section -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="مرحباً بك في مدير الحياة الشخصية" 
                       FontSize="24" FontWeight="Bold" 
                       Foreground="{StaticResource PrimaryTextBrush}"/>
            <TextBlock Text="هنا يمكنك إدارة مهامك، كورساتك، وعاداتك في مكان واحد" 
                       FontSize="14" 
                       Foreground="{StaticResource PrimaryTextBrush}" 
                       Opacity="0.7"
                       Margin="0,5,0,0"/>
        </StackPanel>
        
        <!-- Stats Cards -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Tasks Stats -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,10,0" UniformCornerRadius="8" Background="#2D2D2D">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="CheckboxMarkedCircleOutline" 
                                                 Width="24" Height="24" 
                                                 Foreground="{StaticResource AccentBrush}"/>
                        <TextBlock Text="المهام" 
                                   FontSize="16" FontWeight="Medium" 
                                   Foreground="{StaticResource PrimaryTextBrush}" 
                                   Margin="10,0,0,0"/>
                    </StackPanel>
                    
                    <TextBlock Grid.Row="1" 
                               x:Name="TasksCountText"
                               Text="0" 
                               FontSize="36" FontWeight="Bold" 
                               Foreground="{StaticResource PrimaryTextBrush}" 
                               Margin="0,10,0,5"/>
                    
                    <TextBlock Grid.Row="2" 
                               x:Name="TasksCompletedText"
                               Text="0 مكتملة" 
                               FontSize="14" 
                               Foreground="{StaticResource PrimaryTextBrush}" 
                               Opacity="0.7"/>
                </Grid>
            </materialDesign:Card>
            
            <!-- Courses Stats -->
            <materialDesign:Card Grid.Column="1" Margin="10,0" UniformCornerRadius="8" Background="#2D2D2D">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="School" 
                                                 Width="24" Height="24" 
                                                 Foreground="{StaticResource AccentBrush}"/>
                        <TextBlock Text="الكورسات" 
                                   FontSize="16" FontWeight="Medium" 
                                   Foreground="{StaticResource PrimaryTextBrush}" 
                                   Margin="10,0,0,0"/>
                    </StackPanel>
                    
                    <TextBlock Grid.Row="1" 
                               x:Name="CoursesCountText"
                               Text="0" 
                               FontSize="36" FontWeight="Bold" 
                               Foreground="{StaticResource PrimaryTextBrush}" 
                               Margin="0,10,0,5"/>
                    
                    <TextBlock Grid.Row="2" 
                               x:Name="CoursesProgressText"
                               Text="0% مكتمل" 
                               FontSize="14" 
                               Foreground="{StaticResource PrimaryTextBrush}" 
                               Opacity="0.7"/>
                </Grid>
            </materialDesign:Card>
            
            <!-- Habits Stats -->
            <materialDesign:Card Grid.Column="2" Margin="10,0" UniformCornerRadius="8" Background="#2D2D2D">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Repeat" 
                                                 Width="24" Height="24" 
                                                 Foreground="{StaticResource AccentBrush}"/>
                        <TextBlock Text="العادات" 
                                   FontSize="16" FontWeight="Medium" 
                                   Foreground="{StaticResource PrimaryTextBrush}" 
                                   Margin="10,0,0,0"/>
                    </StackPanel>
                    
                    <TextBlock Grid.Row="1" 
                               x:Name="HabitsCountText"
                               Text="0" 
                               FontSize="36" FontWeight="Bold" 
                               Foreground="{StaticResource PrimaryTextBrush}" 
                               Margin="0,10,0,5"/>
                    
                    <TextBlock Grid.Row="2" 
                               x:Name="HabitsTrackedTodayText"
                               Text="0 متابعة اليوم" 
                               FontSize="14" 
                               Foreground="{StaticResource PrimaryTextBrush}" 
                               Opacity="0.7"/>
                </Grid>
            </materialDesign:Card>
            
            <!-- Calendar Stats -->
            <materialDesign:Card Grid.Column="3" Margin="10,0,0,0" UniformCornerRadius="8" Background="#2D2D2D">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Calendar" 
                                                 Width="24" Height="24" 
                                                 Foreground="{StaticResource AccentBrush}"/>
                        <TextBlock Text="اليوم" 
                                   FontSize="16" FontWeight="Medium" 
                                   Foreground="{StaticResource PrimaryTextBrush}" 
                                   Margin="10,0,0,0"/>
                    </StackPanel>
                    
                    <TextBlock Grid.Row="1" 
                               x:Name="TodayDateText"
                               Text="{Binding Source={x:Static System:DateTime.Now}, StringFormat='{}{0:dd MMM}'}" 
                               FontSize="36" FontWeight="Bold" 
                               Foreground="{StaticResource PrimaryTextBrush}" 
                               Margin="0,10,0,5"/>
                    
                    <TextBlock Grid.Row="2" 
                               x:Name="TodayEventsText"
                               Text="0 أحداث اليوم" 
                               FontSize="14" 
                               Foreground="{StaticResource PrimaryTextBrush}" 
                               Opacity="0.7"/>
                </Grid>
            </materialDesign:Card>
        </Grid>
        
        <!-- Main Content -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Upcoming Tasks -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,10,0" UniformCornerRadius="8" Background="#2D2D2D">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" 
                               Text="المهام القادمة" 
                               Style="{StaticResource SectionTitle}" 
                               Margin="0,0,0,15"/>
                    
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel x:Name="UpcomingTasksPanel">
                            <!-- Tasks will be added here dynamically -->
                            <TextBlock Text="لا توجد مهام قادمة" 
                                       Foreground="{StaticResource PrimaryTextBrush}" 
                                       Opacity="0.7"
                                       HorizontalAlignment="Center"
                                       Margin="0,20,0,0"
                                       x:Name="NoTasksText"/>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </materialDesign:Card>
            
            <!-- Habits Tracking -->
            <materialDesign:Card Grid.Column="1" Margin="10,0,0,0" UniformCornerRadius="8" Background="#2D2D2D">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" 
                               Text="متابعة العادات اليوم" 
                               Style="{StaticResource SectionTitle}" 
                               Margin="0,0,0,15"/>
                    
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel x:Name="TodayHabitsPanel">
                            <!-- Habits will be added here dynamically -->
                            <TextBlock Text="لا توجد عادات للمتابعة" 
                                       Foreground="{StaticResource PrimaryTextBrush}" 
                                       Opacity="0.7"
                                       HorizontalAlignment="Center"
                                       Margin="0,20,0,0"
                                       x:Name="NoHabitsText"/>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </materialDesign:Card>
        </Grid>
    </Grid>
</Page>