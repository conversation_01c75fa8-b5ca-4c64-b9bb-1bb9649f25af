<Page x:Class="PersonalLifeManager.Views.Pages.TasksPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:PersonalLifeManager.Views.Pages"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="800"
      Title="TasksPage"
      Loaded="Page_Loaded">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Action Bar -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- Filter Controls -->
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <ComboBox x:Name="FilterComboBox" 
                          Width="150"
                          materialDesign:HintAssist.Hint="تصفية حسب"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          SelectionChanged="FilterComboBox_SelectionChanged">
                    <ComboBoxItem Content="الكل" IsSelected="True"/>
                    <ComboBoxItem Content="قيد التنفيذ"/>
                    <ComboBoxItem Content="مكتملة"/>
                    <ComboBoxItem Content="أولوية عالية"/>
                    <ComboBoxItem Content="اليوم"/>
                    <ComboBoxItem Content="هذا الأسبوع"/>
                </ComboBox>
                
                <TextBox x:Name="SearchBox" 
                         Width="200"
                         Margin="10,0,0,0"
                         materialDesign:HintAssist.Hint="بحث..."
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         TextChanged="SearchBox_TextChanged"/>
            </StackPanel>
            
            <!-- Add Task Button -->
            <Button Grid.Column="1" 
                    x:Name="AddTaskButton"
                    Content="مهمة جديدة"
                    Style="{StaticResource ActionButton}"
                    Click="AddTaskButton_Click">
                <Button.ContentTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" 
                                                     Width="18" Height="18" 
                                                     VerticalAlignment="Center"/>
                            <TextBlock Text="مهمة جديدة" 
                                       Margin="8,0,0,0" 
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </Button.ContentTemplate>
            </Button>
        </Grid>
        
        <!-- Tasks List -->
        <Grid Grid.Row="1">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel x:Name="TasksPanel">
                    <!-- Tasks will be added here dynamically -->
                    <TextBlock x:Name="NoTasksText" 
                               Text="لا توجد مهام للعرض" 
                               HorizontalAlignment="Center" 
                               VerticalAlignment="Center"
                               Foreground="{StaticResource PrimaryTextBrush}" 
                               Opacity="0.7"
                               Margin="0,50,0,0"
                               Visibility="Collapsed"/>
                </StackPanel>
            </ScrollViewer>
        </Grid>
        
        <!-- Add/Edit Task Dialog -->
        <materialDesign:DialogHost x:Name="TaskDialogHost" 
                                   CloseOnClickAway="True"
                                   Grid.RowSpan="2">
            <materialDesign:DialogHost.DialogContent>
                <Grid Width="400" Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Dialog Title -->
                    <TextBlock x:Name="DialogTitle" 
                               Text="إضافة مهمة جديدة" 
                               FontSize="18" FontWeight="Bold"
                               Margin="0,0,0,20"
                               Grid.Row="0"/>
                    
                    <!-- Task Title -->
                    <TextBox x:Name="TaskTitleBox" 
                             Grid.Row="1"
                             materialDesign:HintAssist.Hint="عنوان المهمة"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,0,0,15"/>
                    
                    <!-- Task Description -->
                    <TextBox x:Name="TaskDescriptionBox" 
                             Grid.Row="2"
                             materialDesign:HintAssist.Hint="وصف المهمة (اختياري)"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             TextWrapping="Wrap"
                             AcceptsReturn="True"
                             VerticalScrollBarVisibility="Auto"
                             Height="80"
                             Margin="0,0,0,15"/>
                    
                    <!-- Due Date -->
                    <DatePicker x:Name="TaskDueDatePicker" 
                                Grid.Row="3"
                                materialDesign:HintAssist.Hint="تاريخ الاستحقاق"
                                Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                Margin="0,0,0,15"/>
                    
                    <!-- Priority -->
                    <ComboBox x:Name="TaskPriorityComboBox" 
                              Grid.Row="4"
                              materialDesign:HintAssist.Hint="الأولوية"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              Margin="0,0,0,20">
                        <ComboBoxItem Content="منخفضة" Tag="0"/>
                        <ComboBoxItem Content="متوسطة" Tag="1" IsSelected="True"/>
                        <ComboBoxItem Content="عالية" Tag="2"/>
                    </ComboBox>
                    
                    <!-- Action Buttons -->
                    <StackPanel Grid.Row="5" 
                                Orientation="Horizontal" 
                                HorizontalAlignment="Right">
                        <Button Content="إلغاء" 
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,10,0"
                                Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"/>
                        
                        <Button x:Name="SaveTaskButton" 
                                Content="حفظ" 
                                Style="{StaticResource ActionButton}"
                                Click="SaveTaskButton_Click"/>
                    </StackPanel>
                </Grid>
            </materialDesign:DialogHost.DialogContent>
        </materialDesign:DialogHost>
        
        <!-- Delete Confirmation Dialog -->
        <materialDesign:DialogHost x:Name="DeleteDialogHost" 
                                   CloseOnClickAway="True"
                                   Grid.RowSpan="2">
            <materialDesign:DialogHost.DialogContent>
                <Grid Width="300" Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Dialog Title -->
                    <TextBlock Text="تأكيد الحذف" 
                               FontSize="18" FontWeight="Bold"
                               Margin="0,0,0,20"
                               Grid.Row="0"/>
                    
                    <!-- Confirmation Message -->
                    <TextBlock Text="هل أنت متأكد من حذف هذه المهمة؟" 
                               Grid.Row="1"
                               TextWrapping="Wrap"
                               Margin="0,0,0,20"/>
                    
                    <!-- Action Buttons -->
                    <StackPanel Grid.Row="2" 
                                Orientation="Horizontal" 
                                HorizontalAlignment="Right">
                        <Button Content="إلغاء" 
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,10,0"
                                Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"/>
                        
                        <Button x:Name="ConfirmDeleteButton" 
                                Content="حذف" 
                                Background="#FF5252"
                                BorderBrush="#FF5252"
                                Foreground="White"
                                Click="ConfirmDeleteButton_Click"/>
                    </StackPanel>
                </Grid>
            </materialDesign:DialogHost.DialogContent>
        </materialDesign:DialogHost>
    </Grid>
</Page>