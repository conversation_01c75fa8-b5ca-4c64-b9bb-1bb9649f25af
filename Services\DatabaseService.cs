using System;
using System.Collections.Generic;
// using System.Data.SQLite; // تم تعطيله مؤقتاً
using System.IO;
using PersonalLifeManager.Models;

namespace PersonalLifeManager.Services
{
    public static class DatabaseService
    {
        private static readonly string DbPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "PersonalLifeManager", "plm_database.db");

        private static readonly string ConnectionString = $"Data Source={DbPath};Version=3;";

        public static void Initialize()
        {
            // تم تعطيل قاعدة البيانات مؤقتاً
            // bool newDatabase = !File.Exists(DbPath);

            // if (newDatabase)
            // {
            //     SQLiteConnection.CreateFile(DbPath);
            //     CreateTables();
            // }
        }

        private static void CreateTables()
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();

                // Create Tasks table
                string createTasksTable = @"
                    CREATE TABLE IF NOT EXISTS Tasks (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Title TEXT NOT NULL,
                        Description TEXT,
                        DueDate TEXT,
                        Priority INTEGER,
                        IsCompleted INTEGER DEFAULT 0,
                        CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP
                    );
                ";

                // Create Courses table
                string createCoursesTable = @"
                    CREATE TABLE IF NOT EXISTS Courses (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Source TEXT,
                        TotalLessons INTEGER,
                        CompletedLessons INTEGER DEFAULT 0,
                        CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP
                    );
                ";

                // Create Habits table
                string createHabitsTable = @"
                    CREATE TABLE IF NOT EXISTS Habits (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Description TEXT,
                        CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP
                    );
                ";

                // Create HabitTracking table
                string createHabitTrackingTable = @"
                    CREATE TABLE IF NOT EXISTS HabitTracking (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        HabitId INTEGER,
                        Date TEXT,
                        FOREIGN KEY (HabitId) REFERENCES Habits(Id) ON DELETE CASCADE
                    );
                ";

                // Create User table with default password
                string createUserTable = @"
                    CREATE TABLE IF NOT EXISTS User (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Password TEXT NOT NULL
                    );
                    
                    INSERT INTO User (Password) VALUES ('1141004');
                ";

                using (var command = new SQLiteCommand(connection))
                {
                    command.CommandText = createTasksTable;
                    command.ExecuteNonQuery();

                    command.CommandText = createCoursesTable;
                    command.ExecuteNonQuery();

                    command.CommandText = createHabitsTable;
                    command.ExecuteNonQuery();

                    command.CommandText = createHabitTrackingTable;
                    command.ExecuteNonQuery();

                    command.CommandText = createUserTable;
                    command.ExecuteNonQuery();
                }
            }
        }

        #region Tasks Methods

        public static List<TaskItem> GetAllTasks()
        {
            // إرجاع بيانات وهمية مؤقتاً
            var tasks = new List<TaskItem>
            {
                new TaskItem
                {
                    Id = 1,
                    Title = "مهمة تجريبية",
                    Description = "هذه مهمة تجريبية للاختبار",
                    DueDate = DateTime.Today.AddDays(1),
                    Priority = TaskPriority.Medium,
                    IsCompleted = false,
                    CreatedAt = DateTime.Now
                }
            };

            return tasks;
        }

        public static List<TaskItem> GetTasksByDate(DateTime date)
        {
            var tasks = new List<TaskItem>();
            string dateString = date.ToString("yyyy-MM-dd");

            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Tasks WHERE date(DueDate) = date(@Date) ORDER BY Priority";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Date", dateString);

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            tasks.Add(new TaskItem
                            {
                                Id = reader.GetInt32(0),
                                Title = reader.GetString(1),
                                Description = reader.IsDBNull(2) ? null : reader.GetString(2),
                                DueDate = reader.IsDBNull(3) ? null : DateTime.Parse(reader.GetString(3)),
                                Priority = (TaskPriority)reader.GetInt32(4),
                                IsCompleted = reader.GetInt32(5) == 1,
                                CreatedAt = DateTime.Parse(reader.GetString(6))
                            });
                        }
                    }
                }
            }

            return tasks;
        }

        public static void AddTask(TaskItem task)
        {
            // تم تعطيل قاعدة البيانات مؤقتاً
            task.Id = new Random().Next(1000, 9999);
        }

        public static void UpdateTask(TaskItem task)
        {
            // تم تعطيل قاعدة البيانات مؤقتاً
        }

        public static void DeleteTask(int taskId)
        {
            // تم تعطيل قاعدة البيانات مؤقتاً
            }
        }

        #endregion

        #region Courses Methods

        public static List<Course> GetAllCourses()
        {
            // إرجاع بيانات وهمية مؤقتاً
            return new List<Course>();
        }

        public static void AddCourse(Course course) { /* تم تعطيل قاعدة البيانات مؤقتاً */ }
        public static void UpdateCourse(Course course) { /* تم تعطيل قاعدة البيانات مؤقتاً */ }

        public static void DeleteCourse(int courseId) { /* تم تعطيل قاعدة البيانات مؤقتاً */ }

        #endregion

        #region Habits Methods

        public static List<Habit> GetAllHabits()
        {
            // إرجاع بيانات وهمية مؤقتاً
            return new List<Habit>();
        }

        public static void AddHabit(Habit habit) { /* تم تعطيل قاعدة البيانات مؤقتاً */ }
        public static void UpdateHabit(Habit habit) { /* تم تعطيل قاعدة البيانات مؤقتاً */ }
        public static void DeleteHabit(int habitId) { /* تم تعطيل قاعدة البيانات مؤقتاً */ }

        public static void TrackHabit(int habitId, DateTime date) { /* تم تعطيل قاعدة البيانات مؤقتاً */ }

        #endregion

        #region Authentication

        public static bool ValidatePassword(string password)
        {
            // إرجاع true مؤقتاً للاختبار
            return true;
        }

        #endregion
    }
}