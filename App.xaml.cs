using System;
using System.IO;
using System.Windows;
using PersonalLifeManager.Services;

namespace PersonalLifeManager
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            
            // Ensure application data directory exists
            string appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "PersonalLifeManager");
            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }
            
            // Initialize database
            DatabaseService.Initialize();
        }
    }
}