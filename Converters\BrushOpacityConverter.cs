using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace PersonalLifeManager
{
    public class BrushOpacityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is SolidColorBrush brush)
            {
                double opacity = 0.7; // Default opacity
                if (parameter != null && double.TryParse(parameter.ToString(), out double paramOpacity))
                {
                    opacity = paramOpacity;
                }

                Color color = brush.Color;
                return new SolidColorBrush(Color.FromArgb((byte)(255 * opacity), color.R, color.G, color.B));
            }

            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}