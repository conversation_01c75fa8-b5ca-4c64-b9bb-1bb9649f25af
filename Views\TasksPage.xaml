<Page x:Class="PersonalLifeManager.Views.TasksPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:PersonalLifeManager.Views"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:models="clr-namespace:PersonalLifeManager.Models"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="800"
      FlowDirection="RightToLeft"
      Title="TasksPage">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Add Task Panel -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,20" Padding="15" UniformCornerRadius="8">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="إضافة مهمة جديدة" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>
                
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="TaskTitleInput" Grid.Column="0" materialDesign:HintAssist.Hint="عنوان المهمة" Margin="0,0,10,0"/>
                    
                    <DatePicker x:Name="TaskDueDatePicker" Grid.Column="1" materialDesign:HintAssist.Hint="تاريخ الاستحقاق" Width="150" Margin="0,0,10,0"/>
                    
                    <ComboBox x:Name="TaskPriorityComboBox" Grid.Column="2" materialDesign:HintAssist.Hint="الأولوية" Width="120">
                        <ComboBoxItem Content="منخفضة" Tag="Low"/>
                        <ComboBoxItem Content="متوسطة" Tag="Medium" IsSelected="True"/>
                        <ComboBoxItem Content="عالية" Tag="High"/>
                    </ComboBox>
                </Grid>
                
                <Grid Grid.Row="2" Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="TaskDescriptionInput" Grid.Column="0" materialDesign:HintAssist.Hint="وصف المهمة (اختياري)" TextWrapping="Wrap" Margin="0,0,10,0"/>
                    
                    <Button x:Name="AddTaskButton" Grid.Column="1" Content="إضافة المهمة" Style="{StaticResource ActionButton}" Click="AddTaskButton_Click"/>
                </Grid>
            </Grid>
        </materialDesign:Card>

        <!-- Tasks List -->
        <materialDesign:Card Grid.Row="1" Padding="15" UniformCornerRadius="8">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="قائمة المهام" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>
                
                <ListView x:Name="TasksListView" Grid.Row="1" ItemsSource="{Binding Tasks}">
                    <ListView.ItemTemplate>
                        <DataTemplate DataType="{x:Type models:TaskItem}">
                            <materialDesign:Card Margin="0,5" UniformCornerRadius="4" Padding="10">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <CheckBox Grid.Column="0" IsChecked="{Binding IsCompleted}" Checked="TaskCheckBox_Checked" Unchecked="TaskCheckBox_Unchecked" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    
                                    <StackPanel Grid.Column="1" Margin="0,0,10,0">
                                        <TextBlock Text="{Binding Title}" FontWeight="SemiBold" TextDecorations="{Binding TitleTextDecoration}"/>
                                        <TextBlock Text="{Binding Description}" TextWrapping="Wrap" Opacity="0.7" Margin="0,5,0,0" TextDecorations="{Binding DescriptionTextDecoration}"/>
                                        <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                            <materialDesign:PackIcon Kind="Calendar" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                            <TextBlock Text="{Binding DueDateFormatted}" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                            <Border Background="{Binding PriorityColor}" CornerRadius="4" Padding="5,2">
                                                <TextBlock Text="{Binding PriorityText}" Foreground="White" FontSize="11"/>
                                            </Border>
                                        </StackPanel>
                                    </StackPanel>
                                    
                                    <Button Grid.Column="2" Style="{StaticResource MaterialDesignFlatButton}" ToolTip="تعديل المهمة" Click="EditTaskButton_Click" Margin="0,0,5,0">
                                        <materialDesign:PackIcon Kind="Edit" Width="20" Height="20"/>
                                    </Button>
                                    
                                    <Button Grid.Column="3" Style="{StaticResource MaterialDesignFlatButton}" ToolTip="حذف المهمة" Click="DeleteTaskButton_Click">
                                        <materialDesign:PackIcon Kind="Delete" Width="20" Height="20"/>
                                    </Button>
                                </Grid>
                            </materialDesign:Card>
                        </DataTemplate>
                    </ListView.ItemTemplate>
                </ListView>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Page>