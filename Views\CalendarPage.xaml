<Page x:Class="PersonalLifeManager.Views.CalendarPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:PersonalLifeManager.Views"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:models="clr-namespace:PersonalLifeManager.Models"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="800"
      FlowDirection="RightToLeft"
      Title="CalendarPage">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="300"/>
        </Grid.ColumnDefinitions>

        <!-- Month Navigation -->
        <Grid Grid.Row="0" Grid.Column="0" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="0" Style="{StaticResource MaterialDesignFlatButton}" Click="PreviousMonthButton_Click">
                <materialDesign:PackIcon Kind="ChevronLeft" Width="24" Height="24"/>
            </Button>
            
            <TextBlock Grid.Column="1" x:Name="MonthYearText" Text="شهر السنة" FontSize="20" FontWeight="SemiBold" HorizontalAlignment="Center" VerticalAlignment="Center"/>
            
            <Button Grid.Column="2" Style="{StaticResource MaterialDesignFlatButton}" Click="NextMonthButton_Click">
                <materialDesign:PackIcon Kind="ChevronRight" Width="24" Height="24"/>
            </Button>
        </Grid>

        <!-- Selected Day Title -->
        <TextBlock Grid.Row="0" Grid.Column="1" x:Name="SelectedDayText" Text="اليوم المحدد" FontSize="18" FontWeight="SemiBold" Margin="15,0,0,15"/>

        <!-- Calendar Grid -->
        <materialDesign:Card Grid.Row="1" Grid.Column="0" UniformCornerRadius="8" Padding="15">
            <Grid>
                <!-- Days of Week Header -->
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <Grid Grid.Row="0" Margin="0,0,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="الأحد" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                    <TextBlock Grid.Column="1" Text="الإثنين" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                    <TextBlock Grid.Column="2" Text="الثلاثاء" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                    <TextBlock Grid.Column="3" Text="الأربعاء" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                    <TextBlock Grid.Column="4" Text="الخميس" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                    <TextBlock Grid.Column="5" Text="الجمعة" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                    <TextBlock Grid.Column="6" Text="السبت" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                </Grid>
                
                <!-- Calendar Days -->
                <ItemsControl Grid.Row="1" x:Name="CalendarItemsControl" ItemsSource="{Binding CalendarDays}">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <UniformGrid Rows="6" Columns="7"/>
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Margin="2" Background="{Binding Background}" BorderBrush="{Binding BorderBrush}" BorderThickness="{Binding BorderThickness}" CornerRadius="4">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Grid.Row="0" Text="{Binding Day}" HorizontalAlignment="Center" Padding="5,5,5,0" FontWeight="{Binding DayFontWeight}"/>
                                    
                                    <ItemsControl Grid.Row="1" ItemsSource="{Binding Indicators}" Margin="5,0,5,5">
                                        <ItemsControl.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center"/>
                                            </ItemsPanelTemplate>
                                        </ItemsControl.ItemsPanel>
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Ellipse Width="8" Height="8" Fill="{Binding Color}" Margin="2,0"/>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </Grid>
        </materialDesign:Card>

        <!-- Day Details -->
        <materialDesign:Card Grid.Row="1" Grid.Column="1" UniformCornerRadius="8" Padding="15" Margin="15,0,0,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="تفاصيل اليوم" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>
                
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- Tasks Section -->
                        <Expander Header="المهام" IsExpanded="True" Margin="0,0,0,10">
                            <ListView x:Name="DayTasksListView" ItemsSource="{Binding SelectedDayTasks}">
                                <ListView.ItemTemplate>
                                    <DataTemplate DataType="{x:Type models:TaskItem}">
                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <Border Grid.Column="0" Background="{Binding PriorityColor}" Width="4" Height="Auto" Margin="0,0,10,0" CornerRadius="2"/>
                                            
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Title}" FontWeight="SemiBold" TextDecorations="{Binding TitleTextDecoration}"/>
                                                <TextBlock Text="{Binding Description}" TextWrapping="Wrap" Opacity="0.7" Margin="0,5,0,0" TextDecorations="{Binding DescriptionTextDecoration}"/>
                                            </StackPanel>
                                        </Grid>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                                <ListView.ItemContainerStyle>
                                    <Style TargetType="ListViewItem">
                                        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                        <Setter Property="Padding" Value="5"/>
                                    </Style>
                                </ListView.ItemContainerStyle>
                            </ListView>
                        </Expander>
                        
                        <!-- Habits Section -->
                        <Expander Header="العادات" IsExpanded="True">
                            <ListView x:Name="DayHabitsListView" ItemsSource="{Binding SelectedDayHabits}">
                                <ListView.ItemTemplate>
                                    <DataTemplate DataType="{x:Type models:Habit}">
                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <CheckBox Grid.Column="0" IsChecked="{Binding IsSelectedDayTracked}" Checked="HabitCheckBox_Checked" Unchecked="HabitCheckBox_Unchecked" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                            
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Name}" FontWeight="SemiBold"/>
                                                <TextBlock Text="{Binding Description}" TextWrapping="Wrap" Opacity="0.7" Margin="0,5,0,0"/>
                                            </StackPanel>
                                            
                                            <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                                                <materialDesign:PackIcon Kind="Fire" Width="16" Height="16" Foreground="{StaticResource AccentColor}" VerticalAlignment="Center"/>
                                                <TextBlock Text="{Binding CurrentStreak}" FontWeight="Bold" Margin="5,0,0,0" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Grid>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                                <ListView.ItemContainerStyle>
                                    <Style TargetType="ListViewItem">
                                        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                        <Setter Property="Padding" Value="5"/>
                                    </Style>
                                </ListView.ItemContainerStyle>
                            </ListView>
                        </Expander>
                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Page>