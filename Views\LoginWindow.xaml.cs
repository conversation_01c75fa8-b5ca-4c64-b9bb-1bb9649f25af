using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using System.Media;
using PersonalLifeManager.Services;

namespace PersonalLifeManager.Views
{
    public partial class LoginWindow : Window
    {
        private const string CORRECT_PASSWORD = "1141004";
        private readonly DispatcherTimer _loginSuccessTimer;
        
        public LoginWindow()
        {
            InitializeComponent();
            
            // Setup success timer
            _loginSuccessTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1.5)
            };
            _loginSuccessTimer.Tick += LoginSuccessTimer_Tick;
            
            // Set initial focus to password box
            PasswordBox.Focus();
        }

        private void PasswordBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                LoginButton_Click(sender, e);
            }
        }
        
        private void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            string password = PasswordBox.Password;
            
            if (password == CORRECT_PASSWORD)
            {
                // Password is correct, show success animation
                SuccessIcon.Visibility = Visibility.Visible;
                
                // Play success sound
                try
                {
                    SystemSounds.Asterisk.Play();
                }
                catch (Exception) { /* Ignore sound errors */ }
                
                // Play success animation
                var successAnimation = (Storyboard)FindResource("SuccessAnimation");
                successAnimation.Begin();
                
                // Start timer to open main window
                _loginSuccessTimer.Start();
                
                // Disable login button to prevent multiple clicks
                LoginButton.IsEnabled = false;
            }
            else
            {
                // Password is incorrect, show error and play shake animation
                ErrorMessage.Visibility = Visibility.Visible;
                
                // Play error sound
                try
                {
                    SystemSounds.Hand.Play();
                }
                catch (Exception) { /* Ignore sound errors */ }
                
                // Play shake animation
                var shakeAnimation = (Storyboard)FindResource("ShakeAnimation");
                shakeAnimation.Begin();
                
                // Clear password and set focus
                PasswordBox.Password = "";
                PasswordBox.Focus();
            }
        }
        
        private void LoginSuccessTimer_Tick(object sender, EventArgs e)
        {
            _loginSuccessTimer.Stop();
            
            // Create fade-out animation for smooth transition
            DoubleAnimation fadeOutAnimation = new DoubleAnimation
            {
                From = 1.0,
                To = 0.0,
                Duration = TimeSpan.FromSeconds(0.5),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };
            
            fadeOutAnimation.Completed += (s, _) =>
            {
                // Open main window
                var mainWindow = new MainWindow();
                mainWindow.Opacity = 0;
                mainWindow.Show();
                
                // Create fade-in animation for main window
                DoubleAnimation fadeInAnimation = new DoubleAnimation
                {
                    From = 0.0,
                    To = 1.0,
                    Duration = TimeSpan.FromSeconds(0.5),
                    EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
                };
                
                mainWindow.BeginAnimation(OpacityProperty, fadeInAnimation);
                this.Close();
            };
            
            this.BeginAnimation(OpacityProperty, fadeOutAnimation);
        }
    }
}