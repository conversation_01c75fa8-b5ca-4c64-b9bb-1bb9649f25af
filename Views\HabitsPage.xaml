<Page x:Class="PersonalLifeManager.Views.HabitsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:PersonalLifeManager.Views"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:models="clr-namespace:PersonalLifeManager.Models"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="800"
      FlowDirection="RightToLeft"
      Title="HabitsPage">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Add Habit Panel -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,20" Padding="15" UniformCornerRadius="8">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="إضافة عادة جديدة" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>
                
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="HabitNameInput" Grid.Column="0" materialDesign:HintAssist.Hint="اسم العادة" Margin="0,0,10,0"/>
                    
                    <TextBox x:Name="HabitDescriptionInput" Grid.Column="1" materialDesign:HintAssist.Hint="وصف العادة (اختياري)" Margin="0,0,10,0"/>
                    
                    <Button x:Name="AddHabitButton" Grid.Column="2" Content="إضافة العادة" Style="{StaticResource ActionButton}" Click="AddHabitButton_Click"/>
                </Grid>
            </Grid>
        </materialDesign:Card>

        <!-- Habits List -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <ItemsControl x:Name="HabitsItemsControl" ItemsSource="{Binding Habits}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate DataType="{x:Type models:Habit}">
                        <materialDesign:Card Margin="0,0,0,15" UniformCornerRadius="8" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- Habit Title and Description -->
                                <StackPanel Grid.Row="0" Grid.Column="0">
                                    <TextBlock Text="{Binding Name}" FontSize="18" FontWeight="SemiBold"/>
                                    <TextBlock Text="{Binding Description}" Opacity="0.7" Margin="0,5,0,0"/>
                                </StackPanel>
                                
                                <!-- Delete Button -->
                                <Button Grid.Row="0" Grid.Column="1" Style="{StaticResource MaterialDesignFlatButton}" ToolTip="حذف العادة" Click="DeleteHabitButton_Click">
                                    <materialDesign:PackIcon Kind="Delete" Width="20" Height="20"/>
                                </Button>
                                
                                <!-- Current Streak -->
                                <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" Orientation="Horizontal" Margin="0,10,0,10">
                                    <materialDesign:PackIcon Kind="Fire" Width="24" Height="24" Foreground="{StaticResource AccentColor}" VerticalAlignment="Center"/>
                                    <TextBlock Text="سلسلة الأيام المتتالية: " FontWeight="SemiBold" Margin="5,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding CurrentStreak}" FontWeight="Bold" FontSize="16" VerticalAlignment="Center"/>
                                    <TextBlock Text=" يوم" Margin="5,0,0,0" VerticalAlignment="Center"/>
                                </StackPanel>
                                
                                <!-- Today's Tracking -->
                                <Grid Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" Text="هل أنجزت هذه العادة اليوم؟" VerticalAlignment="Center"/>
                                    
                                    <ToggleButton Grid.Column="1" IsChecked="{Binding IsTrackedToday}" Checked="HabitToggle_Checked" Unchecked="HabitToggle_Unchecked"
                                                  Style="{StaticResource MaterialDesignSwitchToggleButton}" VerticalAlignment="Center"/>
                                </Grid>
                                
                                <!-- Monthly Calendar -->
                                <Border Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="4" Padding="10">
                                    <Grid x:Name="CalendarGrid">
                                        <!-- Calendar header (days of week) -->
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="*"/>
                                        </Grid.RowDefinitions>
                                        <Grid.Triggers>
                                            <EventTrigger RoutedEvent="Grid.Loaded">
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation 
                                                            Storyboard.TargetProperty="Opacity"
                                                            From="0" To="1" Duration="0:0:0.5" />
                                                        <ThicknessAnimation 
                                                            Storyboard.TargetProperty="Margin"
                                                            From="0,-20,0,0" To="0,0,0,0" Duration="0:0:0.5">
                                                            <ThicknessAnimation.EasingFunction>
                                                                <CubicEase EasingMode="EaseOut" />
                                                            </ThicknessAnimation.EasingFunction>
                                                        </ThicknessAnimation>
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </EventTrigger>
                                        </Grid.Triggers>
                                        
                                        <Grid Grid.Row="0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBlock Grid.Column="0" Text="الأحد" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                                            <TextBlock Grid.Column="1" Text="الإثنين" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                                            <TextBlock Grid.Column="2" Text="الثلاثاء" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                                            <TextBlock Grid.Column="3" Text="الأربعاء" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                                            <TextBlock Grid.Column="4" Text="الخميس" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                                            <TextBlock Grid.Column="5" Text="الجمعة" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                                            <TextBlock Grid.Column="6" Text="السبت" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                                        </Grid>
                                        
                                        <!-- Calendar days (will be generated in code-behind) -->
                                        <ItemsControl Grid.Row="1" x:Name="CalendarItemsControl" ItemsSource="{Binding CalendarDays}">
                                            <ItemsControl.ItemsPanel>
                                                <ItemsPanelTemplate>
                                                    <UniformGrid Rows="5" Columns="7"/>
                                                </ItemsPanelTemplate>
                                            </ItemsControl.ItemsPanel>
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <Border Margin="2" Background="{Binding Background}" CornerRadius="4">
                                                        <TextBlock Text="{Binding Day}" HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5"/>
                                                    </Border>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </Grid>
                                </Border>
                            </Grid>
                        </materialDesign:Card>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</Page>