<svg xmlns="http://www.w3.org/2000/svg" width="1000" height="1000" viewBox="0 0 1000 1000">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#262626" />
      <stop offset="100%" stop-color="#333333" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#F39319" />
      <stop offset="100%" stop-color="#F5A742" />
    </linearGradient>
  </defs>
  
  <!-- Background with gradient -->
  <rect width="100%" height="100%" fill="url(#bgGradient)" />
  
  <!-- Abstract wave patterns -->
  <path d="M0,300 C250,250 350,450 500,400 C650,350 750,550 1000,500 L1000,1000 L0,1000 Z" 
        fill="url(#accentGradient)" opacity="0.05" />
  <path d="M0,400 C200,350 300,550 500,500 C700,450 800,650 1000,600 L1000,1000 L0,1000 Z" 
        fill="url(#accentGradient)" opacity="0.05" />
  <path d="M0,500 C150,450 250,650 500,600 C750,550 850,750 1000,700 L1000,1000 L0,1000 Z" 
        fill="url(#accentGradient)" opacity="0.05" />
  
  <!-- Decorative circles -->
  <circle cx="200" cy="200" r="100" fill="#F39319" opacity="0.05" />
  <circle cx="800" cy="300" r="150" fill="#F39319" opacity="0.05" />
  <circle cx="500" cy="150" r="80" fill="#F39319" opacity="0.05" />
  
  <!-- Subtle grid pattern -->
  <g opacity="0.1">
    <line x1="0" y1="100" x2="1000" y2="100" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="0" y1="200" x2="1000" y2="200" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="0" y1="300" x2="1000" y2="300" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="0" y1="400" x2="1000" y2="400" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="0" y1="500" x2="1000" y2="500" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="0" y1="600" x2="1000" y2="600" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="0" y1="700" x2="1000" y2="700" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="0" y1="800" x2="1000" y2="800" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="0" y1="900" x2="1000" y2="900" stroke="#F0F0F0" stroke-width="0.5" />
    
    <line x1="100" y1="0" x2="100" y2="1000" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="200" y1="0" x2="200" y2="1000" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="300" y1="0" x2="300" y2="1000" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="400" y1="0" x2="400" y2="1000" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="500" y1="0" x2="500" y2="1000" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="600" y1="0" x2="600" y2="1000" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="700" y1="0" x2="700" y2="1000" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="800" y1="0" x2="800" y2="1000" stroke="#F0F0F0" stroke-width="0.5" />
    <line x1="900" y1="0" x2="900" y2="1000" stroke="#F0F0F0" stroke-width="0.5" />
  </g>
  
  <!-- Subtle dots pattern -->
  <g opacity="0.2">
    <circle cx="50" cy="50" r="2" fill="#F0F0F0" />
    <circle cx="150" cy="50" r="2" fill="#F0F0F0" />
    <circle cx="250" cy="50" r="2" fill="#F0F0F0" />
    <circle cx="350" cy="50" r="2" fill="#F0F0F0" />
    <circle cx="450" cy="50" r="2" fill="#F0F0F0" />
    <circle cx="550" cy="50" r="2" fill="#F0F0F0" />
    <circle cx="650" cy="50" r="2" fill="#F0F0F0" />
    <circle cx="750" cy="50" r="2" fill="#F0F0F0" />
    <circle cx="850" cy="50" r="2" fill="#F0F0F0" />
    <circle cx="950" cy="50" r="2" fill="#F0F0F0" />
    
    <circle cx="50" cy="150" r="2" fill="#F0F0F0" />
    <circle cx="150" cy="150" r="2" fill="#F0F0F0" />
    <circle cx="250" cy="150" r="2" fill="#F0F0F0" />
    <circle cx="350" cy="150" r="2" fill="#F0F0F0" />
    <circle cx="450" cy="150" r="2" fill="#F0F0F0" />
    <circle cx="550" cy="150" r="2" fill="#F0F0F0" />
    <circle cx="650" cy="150" r="2" fill="#F0F0F0" />
    <circle cx="750" cy="150" r="2" fill="#F0F0F0" />
    <circle cx="850" cy="150" r="2" fill="#F0F0F0" />
    <circle cx="950" cy="150" r="2" fill="#F0F0F0" />
    
    <!-- Repeat for other rows -->
    <!-- Row 3 -->
    <circle cx="50" cy="250" r="2" fill="#F0F0F0" />
    <circle cx="150" cy="250" r="2" fill="#F0F0F0" />
    <circle cx="250" cy="250" r="2" fill="#F0F0F0" />
    <circle cx="350" cy="250" r="2" fill="#F0F0F0" />
    <circle cx="450" cy="250" r="2" fill="#F0F0F0" />
    <circle cx="550" cy="250" r="2" fill="#F0F0F0" />
    <circle cx="650" cy="250" r="2" fill="#F0F0F0" />
    <circle cx="750" cy="250" r="2" fill="#F0F0F0" />
    <circle cx="850" cy="250" r="2" fill="#F0F0F0" />
    <circle cx="950" cy="250" r="2" fill="#F0F0F0" />
  </g>
</svg>