<Page x:Class="PersonalLifeManager.Views.Pages.HabitsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:PersonalLifeManager.Views.Pages"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="800"
      Title="HabitsPage"
      Loaded="Page_Loaded">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Action Bar -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- Month Selector -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left">
                <Button x:Name="PreviousMonthButton" 
                        Style="{StaticResource MaterialDesignFlatButton}">
                    <materialDesign:PackIcon Kind="ChevronLeft" Width="24" Height="24"/>
                </Button>
                
                <TextBlock x:Name="MonthYearText" 
                           Text="يناير 2023" 
                           FontSize="18" 
                           FontWeight="Medium" 
                           VerticalAlignment="Center" 
                           Margin="10,0"/>
                
                <Button x:Name="NextMonthButton" 
                        Style="{StaticResource MaterialDesignFlatButton}">
                    <materialDesign:PackIcon Kind="ChevronRight" Width="24" Height="24"/>
                </Button>
            </StackPanel>
            
            <!-- Add Habit Button -->
            <Button Grid.Column="1" 
                    x:Name="AddHabitButton"
                    Content="عادة جديدة"
                    Style="{StaticResource ActionButton}"
                    Click="AddHabitButton_Click">
                <Button.ContentTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" 
                                                     Width="18" Height="18" 
                                                     VerticalAlignment="Center"/>
                            <TextBlock Text="عادة جديدة" 
                                       Margin="8,0,0,0" 
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </Button.ContentTemplate>
            </Button>
        </Grid>
        
        <!-- Calendar -->
        <Border Grid.Row="1" 
                BorderThickness="1" 
                BorderBrush="#3D3D3D" 
                Margin="0,10,0,20" 
                Padding="10">
            <Grid x:Name="CalendarGrid">
                <!-- Calendar will be generated dynamically -->
            </Grid>
        </Border>
        
        <!-- Habits List -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <StackPanel x:Name="HabitsPanel">
                <!-- Habits will be added here dynamically -->
                <TextBlock x:Name="NoHabitsText" 
                           Text="لا توجد عادات للعرض" 
                           HorizontalAlignment="Center" 
                           VerticalAlignment="Center"
                           Foreground="{StaticResource PrimaryTextBrush}" 
                           Opacity="0.7"
                           Margin="0,50,0,0"
                           Visibility="Collapsed"/>
            </StackPanel>
        </ScrollViewer>
        
        <!-- Add/Edit Habit Dialog -->
        <materialDesign:DialogHost x:Name="HabitDialogHost" 
                                   CloseOnClickAway="True"
                                   Grid.RowSpan="2">
            <materialDesign:DialogHost.DialogContent>
                <Grid Width="400" Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Dialog Title -->
                    <TextBlock x:Name="DialogTitle" 
                               Text="إضافة عادة جديدة" 
                               FontSize="18" FontWeight="Bold"
                               Margin="0,0,0,20"
                               Grid.Row="0"/>
                    
                    <!-- Habit Name -->
                    <TextBox x:Name="HabitNameBox" 
                             Grid.Row="1"
                             materialDesign:HintAssist.Hint="اسم العادة"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,0,0,15"/>
                    
                    <!-- Habit Description -->
                    <TextBox x:Name="HabitDescriptionBox" 
                             Grid.Row="2"
                             materialDesign:HintAssist.Hint="وصف العادة (اختياري)"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,0,0,20"
                             TextWrapping="Wrap"
                             AcceptsReturn="True"
                             VerticalScrollBarVisibility="Auto"
                             Height="80"/>
                    
                    <!-- Action Buttons -->
                    <StackPanel Grid.Row="3" 
                                Orientation="Horizontal" 
                                HorizontalAlignment="Right">
                        <Button Content="إلغاء" 
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,10,0"
                                Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"/>
                        
                        <Button x:Name="SaveHabitButton" 
                                Content="حفظ" 
                                Style="{StaticResource ActionButton}"
                                Click="SaveHabitButton_Click"/>
                    </StackPanel>
                </Grid>
            </materialDesign:DialogHost.DialogContent>
        </materialDesign:DialogHost>
        
        <!-- Delete Confirmation Dialog -->
        <materialDesign:DialogHost x:Name="DeleteDialogHost" 
                                   CloseOnClickAway="True"
                                   Grid.RowSpan="2">
            <materialDesign:DialogHost.DialogContent>
                <Grid Width="300" Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Dialog Title -->
                    <TextBlock Text="تأكيد الحذف" 
                               FontSize="18" FontWeight="Bold"
                               Margin="0,0,0,20"
                               Grid.Row="0"/>
                    
                    <!-- Confirmation Message -->
                    <TextBlock Text="هل أنت متأكد من حذف هذه العادة؟ سيتم حذف جميع بيانات التتبع المرتبطة بها." 
                               Grid.Row="1"
                               TextWrapping="Wrap"
                               Margin="0,0,0,20"/>
                    
                    <!-- Action Buttons -->
                    <StackPanel Grid.Row="2" 
                                Orientation="Horizontal" 
                                HorizontalAlignment="Right">
                        <Button Content="إلغاء" 
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,10,0"
                                Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"/>
                        
                        <Button x:Name="ConfirmDeleteButton" 
                                Content="حذف" 
                                Background="#FF5252"
                                BorderBrush="#FF5252"
                                Foreground="White"
                                Click="ConfirmDeleteButton_Click"/>
                    </StackPanel>
                </Grid>
            </materialDesign:DialogHost.DialogContent>
        </materialDesign:DialogHost>
    </Grid>
</Page>