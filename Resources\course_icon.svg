<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
  <defs>
    <linearGradient id="courseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#F39319" />
      <stop offset="100%" stop-color="#F5A742" />
    </linearGradient>
  </defs>
  
  <!-- Book base -->
  <path d="M4,4 L4,20 L20,20 L20,4 L4,4 Z" fill="#F0F0F0" stroke="#262626" stroke-width="1.5" />
  
  <!-- Book spine -->
  <path d="M4,4 L4,20 C6,18.5 8,18.5 10,20 L10,4 C8,2.5 6,2.5 4,4 Z" fill="url(#courseGradient)" stroke="#262626" stroke-width="1.5" />
  
  <!-- Book pages lines -->
  <line x1="12" y1="8" x2="18" y2="8" stroke="#262626" stroke-width="1" />
  <line x1="12" y1="12" x2="18" y2="12" stroke="#262626" stroke-width="1" />
  <line x1="12" y1="16" x2="16" y2="16" stroke="#262626" stroke-width="1" />
  
  <!-- Graduation cap -->
  <circle cx="16" cy="6" r="2" fill="#262626" opacity="0.8" />
  <rect x="15" y="6" width="2" height="4" fill="#262626" opacity="0.8" />
  <rect x="14" y="10" width="4" height="1" fill="#262626" opacity="0.8" />
</svg>