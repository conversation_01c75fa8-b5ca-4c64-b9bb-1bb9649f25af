using System;

namespace PersonalLifeManager.Models
{
    public class Course
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string? Source { get; set; }
        public int TotalLessons { get; set; }
        public int CompletedLessons { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Helper properties for UI
        public double ProgressPercentage => TotalLessons > 0 ? (double)CompletedLessons / TotalLessons * 100 : 0;
        public string ProgressText => $"{CompletedLessons}/{TotalLessons} ({ProgressPercentage:0.0}%)";
    }
}