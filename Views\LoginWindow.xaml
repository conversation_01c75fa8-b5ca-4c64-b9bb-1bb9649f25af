<Window x:Class="PersonalLifeManager.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="مدير الحياة الشخصية - تسجيل الدخول" 
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="#262626"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        FontFamily="{materialDesign:MaterialDesignFont}"
        FlowDirection="RightToLeft">
    
    <Window.Resources>
        <Storyboard x:Key="ShakeAnimation">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)" 
                                           Storyboard.TargetName="LoginCard">
                <EasingDoubleKeyFrame KeyTime="0:0:0.0" Value="0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="-15"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="15"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="-10"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="10"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="-5"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="5"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0"/>
            </DoubleAnimationUsingKeyFrames>
            <ColorAnimation Storyboard.TargetName="PasswordBox" 
                            Storyboard.TargetProperty="BorderBrush.(SolidColorBrush.Color)"
                            To="#F44336" Duration="0:0:0.2" AutoReverse="True" RepeatBehavior="3x" />
            <ThicknessAnimation Storyboard.TargetName="PasswordBox"
                               Storyboard.TargetProperty="BorderThickness"
                               To="2" Duration="0:0:0.2" AutoReverse="True" RepeatBehavior="3x" />
            <DoubleAnimation Storyboard.TargetName="ErrorMessage"
                            Storyboard.TargetProperty="Opacity"
                            From="0" To="1" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut" />
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
        
        <Storyboard x:Key="SuccessAnimation">
            <!-- Fade out password box and login button -->
            <DoubleAnimation Storyboard.TargetName="PasswordBox" 
                             Storyboard.TargetProperty="Opacity"
                             To="0" Duration="0:0:0.2" />
            <DoubleAnimation Storyboard.TargetName="LoginButton" 
                             Storyboard.TargetProperty="Opacity"
                             To="0" Duration="0:0:0.2" />
            <DoubleAnimation Storyboard.TargetName="ErrorMessage" 
                             Storyboard.TargetProperty="Opacity"
                             To="0" Duration="0:0:0.2" />
            
            <!-- Show success icon with animation -->
            <DoubleAnimation Storyboard.TargetName="SuccessIcon" 
                             Storyboard.TargetProperty="Opacity"
                             From="0" To="1" Duration="0:0:0.3" 
                             BeginTime="0:0:0.2" />
            <DoubleAnimation Storyboard.TargetName="SuccessIcon" 
                             Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)"
                             From="0.5" To="1" Duration="0:0:0.5" 
                             BeginTime="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <ElasticEase EasingMode="EaseOut" Oscillations="3" Springiness="8" />
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="SuccessIcon" 
                             Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)"
                             From="0.5" To="1" Duration="0:0:0.5" 
                             BeginTime="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <ElasticEase EasingMode="EaseOut" Oscillations="3" Springiness="8" />
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="SuccessIcon" 
                             Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(RotateTransform.Angle)"
                             From="-30" To="0" Duration="0:0:0.5" 
                             BeginTime="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <ElasticEase EasingMode="EaseOut" Oscillations="2" Springiness="5" />
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            
            <!-- Card color change animation -->
            <ColorAnimation Storyboard.TargetName="LoginCard" 
                            Storyboard.TargetProperty="Background.(SolidColorBrush.Color)"
                            To="#E8F5E9" Duration="0:0:0.5" 
                            BeginTime="0:0:0.2" />
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- App Logo and Title -->
        <StackPanel Grid.Row="0" Margin="0,40,0,0">
            <Image x:Name="AppLogo" Source="/Resources/G.png" Width="80" Height="80" Margin="0,0,0,10">
                <Image.RenderTransform>
                    <RotateTransform CenterX="40" CenterY="40" Angle="0"/>
                </Image.RenderTransform>
                <Image.Triggers>
                    <EventTrigger RoutedEvent="Image.Loaded">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation 
                                    Storyboard.TargetName="AppLogo" 
                                    Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                                    From="0" To="360" Duration="0:0:3" 
                                    RepeatBehavior="1x" />
                                <DoubleAnimation 
                                    Storyboard.TargetName="AppLogo" 
                                    Storyboard.TargetProperty="Opacity"
                                    From="0" To="1" Duration="0:0:1.5" />
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                </Image.Triggers>
            </Image>
            <TextBlock Text="مدير الحياة الشخصية" 
                       HorizontalAlignment="Center" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       Foreground="#F0F0F0">
                <TextBlock.Triggers>
                    <EventTrigger RoutedEvent="TextBlock.Loaded">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation 
                                    Storyboard.TargetProperty="Opacity"
                                    From="0" To="1" Duration="0:0:1.5" 
                                    BeginTime="0:0:0.5" />
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                </TextBlock.Triggers>
            </TextBlock>
            <TextBlock Text="تنظيم حياتك في مكان واحد" 
                       HorizontalAlignment="Center" 
                       FontSize="14" 
                       Margin="0,5,0,0" 
                       Foreground="#F0F0F0" 
                       Opacity="0">
                <TextBlock.Triggers>
                    <EventTrigger RoutedEvent="TextBlock.Loaded">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation 
                                    Storyboard.TargetProperty="Opacity"
                                    From="0" To="0.7" Duration="0:0:1.5" 
                                    BeginTime="0:0:1" />
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                </TextBlock.Triggers>
            </TextBlock>
        </StackPanel>

        <!-- Login Card -->
        <materialDesign:Card x:Name="LoginCard" 
                             Grid.Row="1" 
                             Margin="30,20,30,40" 
                             Padding="30" 
                             Background="#F0F0F0"
                             UniformCornerRadius="8">
            <LoginCard.RenderTransform>
                <TranslateTransform X="0" Y="0"/>
            </LoginCard.RenderTransform>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Login Title -->
                <TextBlock Grid.Row="0" 
                           Text="تسجيل الدخول" 
                           HorizontalAlignment="Center" 
                           FontSize="18" 
                           FontWeight="Bold" 
                           Margin="0,0,0,20" 
                           Foreground="#262626"/>

                <!-- Password Field -->
                <PasswordBox x:Name="PasswordBox" 
                             Grid.Row="1" 
                             materialDesign:HintAssist.Hint="كلمة المرور" 
                             Style="{StaticResource AnimatedPasswordBox}" 
                             Margin="0,10,0,0" 
                             FontSize="16"
                             KeyDown="PasswordBox_KeyDown"/>

                <!-- Error Message -->
                <TextBlock x:Name="ErrorMessage" 
                           Grid.Row="2" 
                           Text="كلمة المرور غير صحيحة" 
                           Foreground="Red" 
                           HorizontalAlignment="Center" 
                           Margin="0,10,0,0" 
                           Visibility="Collapsed"/>

                <!-- Login Button -->
                <Button x:Name="LoginButton" 
                        Grid.Row="3" 
                        Content="دخول" 
                        Style="{StaticResource MaterialDesignRaisedButton}" 
                        Background="#F39319" 
                        BorderBrush="#F39319" 
                        Foreground="#F0F0F0" 
                        Margin="0,30,0,0" 
                        Height="45" 
                        FontSize="16" 
                        Click="LoginButton_Click"/>

                <!-- Success Icon (Hidden by default) -->
                <materialDesign:PackIcon x:Name="SuccessIcon" 
                                         Kind="CheckCircle" 
                                         Width="60" 
                                         Height="60" 
                                         Foreground="#4CAF50" 
                                         HorizontalAlignment="Center" 
                                         VerticalAlignment="Center" 
                                         Grid.Row="1" 
                                         Grid.RowSpan="3" 
                                         Opacity="0">
                    <materialDesign:PackIcon.RenderTransform>
                        <TransformGroup>
                            <ScaleTransform ScaleX="1" ScaleY="1"/>
                            <RotateTransform Angle="0"/>
                        </TransformGroup>
                    </materialDesign:PackIcon.RenderTransform>
                </materialDesign:PackIcon>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Window>