using System;
using System.Collections.ObjectModel;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;
using PersonalLifeManager.Models;
using PersonalLifeManager.Services;

namespace PersonalLifeManager.Views
{
    public partial class CoursesPage : Page
    {
        private readonly DatabaseService _databaseService;
        public ObservableCollection<Course> Courses { get; set; }

        public CoursesPage()
        {
            InitializeComponent();
            _databaseService = new DatabaseService();
            Courses = new ObservableCollection<Course>();
            DataContext = this;
            LoadCourses();
        }

        private void LoadCourses()
        {
            Courses.Clear();
            var courses = _databaseService.GetAllCourses();
            
            // Add courses with staggered animation
            for (int i = 0; i < courses.Count; i++)
            {
                var course = courses[i];
                Courses.Add(course);
                
                // Apply animations to course items in the ListView
                if (CoursesListView.ItemContainerGenerator.ContainerFromItem(course) is FrameworkElement container)
                {
                    // Set initial state for animation
                    container.Opacity = 0;
                    container.RenderTransform = new System.Windows.Media.TranslateTransform(0, 30);
                    
                    // Create animations
                    var fadeAnimation = new System.Windows.Media.Animation.DoubleAnimation
                    {
                        From = 0,
                        To = 1,
                        Duration = TimeSpan.FromMilliseconds(400),
                        BeginTime = TimeSpan.FromMilliseconds(i * 150) // Stagger the animations
                    };
                    
                    var moveAnimation = new System.Windows.Media.Animation.DoubleAnimation
                    {
                        From = 30,
                        To = 0,
                        Duration = TimeSpan.FromMilliseconds(400),
                        BeginTime = TimeSpan.FromMilliseconds(i * 150),
                        EasingFunction = new System.Windows.Media.Animation.ElasticEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut, Oscillations = 1, Springiness = 3 }
                    };
                    
                    // Start animations
                    container.BeginAnimation(UIElement.OpacityProperty, fadeAnimation);
                    ((System.Windows.Media.TranslateTransform)container.RenderTransform).BeginAnimation(System.Windows.Media.TranslateTransform.YProperty, moveAnimation);
                }
            }
        }

        private void AddCourseButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(CourseNameInput.Text))
            {
                ShowSnackbar("يرجى إدخال اسم الكورس");
                return;
            }

            if (string.IsNullOrWhiteSpace(CourseSourceInput.Text))
            {
                ShowSnackbar("يرجى إدخال مصدر الكورس");
                return;
            }

            if (string.IsNullOrWhiteSpace(CourseTotalLessonsInput.Text) || !int.TryParse(CourseTotalLessonsInput.Text, out int totalLessons) || totalLessons <= 0)
            {
                ShowSnackbar("يرجى إدخال عدد صحيح للدروس");
                return;
            }

            var newCourse = new Course
            {
                Name = CourseNameInput.Text,
                Source = CourseSourceInput.Text,
                TotalLessons = totalLessons,
                CompletedLessons = 0,
                CreatedAt = DateTime.Now
            };

            var courseId = _databaseService.AddCourse(newCourse);
            newCourse.Id = courseId;
            ShowSnackbar("تمت إضافة الكورس بنجاح");

            // Clear inputs
            CourseNameInput.Text = string.Empty;
            CourseSourceInput.Text = string.Empty;
            CourseTotalLessonsInput.Text = string.Empty;

            // Refresh courses list
            LoadCourses();
        }

        private void DeleteCourseButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var course = button.DataContext as Course;

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف الكورس '{course.Name}'؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                _databaseService.DeleteCourse(course.Id);
                ShowSnackbar("تم حذف الكورس بنجاح");
                LoadCourses();
            }
        }

        private void IncrementLessonButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var course = button.DataContext as Course;

            if (course.CompletedLessons < course.TotalLessons)
            {
                course.CompletedLessons++;
                _databaseService.UpdateCourse(course);
                LoadCourses();
            }
        }

        private void DecrementLessonButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var course = button.DataContext as Course;

            if (course.CompletedLessons > 0)
            {
                course.CompletedLessons--;
                _databaseService.UpdateCourse(course);
                LoadCourses();
            }
        }

        private void NumberValidationTextBox(object sender, TextCompositionEventArgs e)
        {
            Regex regex = new Regex("[^0-9]+");
            e.Handled = regex.IsMatch(e.Text);
        }

        private void ShowSnackbar(string message)
        {
            var snackbar = new Snackbar();
            snackbar.MessageQueue.Enqueue(message, null, null, null, false, true, TimeSpan.FromSeconds(3));
        }
    }
}