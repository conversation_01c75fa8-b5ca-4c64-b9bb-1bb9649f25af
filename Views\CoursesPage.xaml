<Page x:Class="PersonalLifeManager.Views.CoursesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:PersonalLifeManager.Views"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:models="clr-namespace:PersonalLifeManager.Models"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="800"
      FlowDirection="RightToLeft"
      Title="CoursesPage">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Add Course Panel -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,20" Padding="15" UniformCornerRadius="8">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="إضافة كورس جديد" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>
                
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="CourseNameInput" Grid.Column="0" materialDesign:HintAssist.Hint="اسم الكورس" Margin="0,0,10,0"/>
                    
                    <TextBox x:Name="CourseSourceInput" Grid.Column="1" materialDesign:HintAssist.Hint="مصدر الكورس" Margin="0,0,10,0"/>
                    
                    <TextBox x:Name="CourseTotalLessonsInput" Grid.Column="2" materialDesign:HintAssist.Hint="عدد الدروس" Width="120" Margin="0,0,10,0"
                             PreviewTextInput="NumberValidationTextBox"/>
                    
                    <Button x:Name="AddCourseButton" Grid.Column="3" Content="إضافة الكورس" Style="{StaticResource ActionButton}" Click="AddCourseButton_Click"/>
                </Grid>
            </Grid>
        </materialDesign:Card>

        <!-- Courses List -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <ItemsControl x:Name="CoursesItemsControl" ItemsSource="{Binding Courses}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate DataType="{x:Type models:Course}">
                        <materialDesign:Card Margin="0,0,0,15" UniformCornerRadius="8" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- Course Title and Source -->
                                <StackPanel Grid.Row="0" Grid.Column="0">
                                    <TextBlock Text="{Binding Name}" FontSize="18" FontWeight="SemiBold"/>
                                    <TextBlock Text="{Binding Source}" Opacity="0.7" Margin="0,5,0,0"/>
                                </StackPanel>
                                
                                <!-- Delete Button -->
                                <Button Grid.Row="0" Grid.Column="1" Style="{StaticResource MaterialDesignFlatButton}" ToolTip="حذف الكورس" Click="DeleteCourseButton_Click">
                                    <materialDesign:PackIcon Kind="Delete" Width="20" Height="20"/>
                                </Button>
                                
                                <!-- Progress Bar -->
                                <ProgressBar Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" Value="{Binding ProgressPercentage}" Margin="0,15,0,5" Height="10"
                                             Foreground="{StaticResource AccentColor}" Background="#E0E0E0"/>
                                
                                <!-- Progress Text and Controls -->
                                <Grid Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" Text="{Binding ProgressText}" VerticalAlignment="Center"/>
                                    
                                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                                        <Button Style="{StaticResource MaterialDesignFlatButton}" ToolTip="إنقاص درس" Click="DecrementLessonButton_Click"
                                                IsEnabled="{Binding CanDecrement}">
                                            <materialDesign:PackIcon Kind="Minus" Width="20" Height="20"/>
                                        </Button>
                                        
                                        <Button Style="{StaticResource MaterialDesignFlatButton}" ToolTip="زيادة درس" Click="IncrementLessonButton_Click"
                                                IsEnabled="{Binding CanIncrement}">
                                            <materialDesign:PackIcon Kind="Plus" Width="20" Height="20"/>
                                        </Button>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </materialDesign:Card>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</Page>