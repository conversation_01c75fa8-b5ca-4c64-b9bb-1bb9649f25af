<Page x:Class="PersonalLifeManager.Views.Pages.CoursesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:PersonalLifeManager.Views.Pages"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="800"
      Loaded="Page_Loaded"
      Title="CoursesPage">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header with Add Course Button -->
        <Grid Grid.Row="0" Margin="20,10,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Text="الكورسات" 
                       FontSize="24" 
                       FontWeight="Medium" 
                       Foreground="#F0F0F0"
                       VerticalAlignment="Center"
                       HorizontalAlignment="Right"/>

            <Button x:Name="AddCourseButton" 
                    Grid.Column="1" 
                    Content="إضافة كورس جديد" 
                    Style="{StaticResource MaterialDesignRaisedButton}" 
                    Background="#F39319" 
                    BorderBrush="#F39319" 
                    Foreground="#262626"
                    materialDesign:ButtonAssist.CornerRadius="8"
                    Height="40"
                    Padding="16,0"
                    Click="AddCourseButton_Click"/>
        </Grid>

        <!-- Courses Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="0,10,0,0">
            <Grid>
                <!-- No Courses Message -->
                <TextBlock x:Name="NoCoursesText" 
                           Text="لا توجد كورسات حالياً. أضف كورس جديد للبدء!" 
                           FontSize="16" 
                           Foreground="#F0F0F0" 
                           HorizontalAlignment="Center" 
                           VerticalAlignment="Center" 
                           Margin="0,50,0,0"
                           Visibility="Collapsed"/>

                <!-- Courses Panel -->
                <WrapPanel x:Name="CoursesPanel" 
                           Orientation="Horizontal" 
                           HorizontalAlignment="Center"
                           Margin="20,0,20,20"/>
            </Grid>
        </ScrollViewer>

        <!-- Add/Edit Course Dialog -->
        <materialDesign:DialogHost x:Name="CourseDialogHost" 
                                   Grid.RowSpan="2" 
                                   CloseOnClickAway="True">
            <materialDesign:DialogHost.DialogContent>
                <Grid Width="400" Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Dialog Title -->
                    <TextBlock x:Name="DialogTitle" 
                               Text="إضافة كورس جديد" 
                               FontSize="18" 
                               FontWeight="Medium" 
                               Margin="0,0,0,20" 
                               HorizontalAlignment="Right"/>

                    <!-- Course Name -->
                    <TextBox x:Name="CourseNameBox" 
                             Grid.Row="1" 
                             materialDesign:HintAssist.Hint="اسم الكورس" 
                             Style="{StaticResource MaterialDesignOutlinedTextBox}" 
                             Margin="0,0,0,15"
                             FlowDirection="RightToLeft"/>

                    <!-- Course Source -->
                    <TextBox x:Name="CourseSourceBox" 
                             Grid.Row="2" 
                             materialDesign:HintAssist.Hint="المصدر (موقع، منصة تعليمية، إلخ)" 
                             Style="{StaticResource MaterialDesignOutlinedTextBox}" 
                             Margin="0,0,0,15"
                             FlowDirection="RightToLeft"/>

                    <!-- Total Lessons -->
                    <TextBox x:Name="TotalLessonsBox" 
                             Grid.Row="3" 
                             materialDesign:HintAssist.Hint="عدد الدروس الإجمالي" 
                             Style="{StaticResource MaterialDesignOutlinedTextBox}" 
                             Margin="0,0,0,15"
                             PreviewTextInput="NumberValidationTextBox"
                             FlowDirection="RightToLeft"/>

                    <!-- Completed Lessons (for edit mode) -->
                    <TextBox x:Name="CompletedLessonsBox" 
                             Grid.Row="4" 
                             materialDesign:HintAssist.Hint="عدد الدروس المكتملة" 
                             Style="{StaticResource MaterialDesignOutlinedTextBox}" 
                             Margin="0,0,0,20"
                             PreviewTextInput="NumberValidationTextBox"
                             FlowDirection="RightToLeft"/>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Row="5" 
                                Orientation="Horizontal" 
                                HorizontalAlignment="Left">
                        <Button Content="إلغاء" 
                                Style="{StaticResource MaterialDesignFlatButton}" 
                                Margin="0,0,10,0" 
                                Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"/>
                        
                        <Button x:Name="SaveCourseButton" 
                                Content="حفظ" 
                                Style="{StaticResource MaterialDesignRaisedButton}" 
                                Background="#F39319" 
                                BorderBrush="#F39319" 
                                Foreground="#262626"
                                Click="SaveCourseButton_Click"/>
                    </StackPanel>
                </Grid>
            </materialDesign:DialogHost.DialogContent>
        </materialDesign:DialogHost>

        <!-- Delete Confirmation Dialog -->
        <materialDesign:DialogHost x:Name="DeleteDialogHost" 
                                   Grid.RowSpan="2" 
                                   CloseOnClickAway="True">
            <materialDesign:DialogHost.DialogContent>
                <Grid Width="300" Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Confirmation Message -->
                    <TextBlock Text="هل أنت متأكد من حذف هذا الكورس؟" 
                               TextWrapping="Wrap" 
                               Margin="0,0,0,20" 
                               HorizontalAlignment="Right"/>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Row="1" 
                                Orientation="Horizontal" 
                                HorizontalAlignment="Left">
                        <Button Content="إلغاء" 
                                Style="{StaticResource MaterialDesignFlatButton}" 
                                Margin="0,0,10,0" 
                                Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"/>
                        
                        <Button x:Name="ConfirmDeleteButton" 
                                Content="حذف" 
                                Style="{StaticResource MaterialDesignRaisedButton}" 
                                Background="#FF5252" 
                                BorderBrush="#FF5252" 
                                Click="ConfirmDeleteButton_Click"/>
                    </StackPanel>
                </Grid>
            </materialDesign:DialogHost.DialogContent>
        </materialDesign:DialogHost>
    </Grid>
</Page>