using System;
using System.Collections.Generic;
using System.Linq;

namespace PersonalLifeManager.Models
{
    public class Habit
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public List<DateTime> TrackingDates { get; set; } = new List<DateTime>();

        // Helper properties for UI
        public bool IsTrackedToday => TrackingDates.Any(d => d.Date == DateTime.Today);
        
        public int CurrentStreak
        {
            get
            {
                if (!TrackingDates.Any())
                    return 0;

                var sortedDates = TrackingDates.Select(d => d.Date).OrderByDescending(d => d).ToList();
                
                // If today is not tracked and yesterday is not tracked, streak is 0
                if (sortedDates[0] < DateTime.Today.AddDays(-1))
                    return 0;
                
                int streak = 1;
                DateTime expectedDate = sortedDates[0].AddDays(-1);

                for (int i = 1; i < sortedDates.Count; i++)
                {
                    if (sortedDates[i] == expectedDate)
                    {
                        streak++;
                        expectedDate = expectedDate.AddDays(-1);
                    }
                    else
                    {
                        break;
                    }
                }

                return streak;
            }
        }

        public bool IsTrackedOnDate(DateTime date)
        {
            return TrackingDates.Any(d => d.Date == date.Date);
        }
    }
}